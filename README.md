# St. Louis Demonstration Junior High School Website

A modern, responsive website for St. Louis Demonstration Junior High School, located in Kumasi, Ghana. This website showcases the school's academic excellence, programs, and community engagement through a beautiful, user-friendly interface.

## 🏫 About the School

St. Louis Demonstration Junior High School is a prestigious public junior high school that has been serving the Kumasi community for over 47 years. As part of the esteemed St. Louis Demonstration J.H.S, the school has built a strong reputation for academic excellence, moral uprightness, and spiritual grounding.

**School Motto:** "UT SINT UNUM – DIEU LE VEUT" ("That they may be one – God wills it")

## 🌟 Key Features

### 📱 Modern Design
- **Responsive Design**: Optimized for all devices (mobile, tablet, desktop)
- **Dark Mode Support**: Consistent dark theme across pages
- **Glass Morphism UI**: Modern glass-like effects and animations
- **Mobile-First Approach**: Prioritizes mobile user experience

### 🎯 Core Functionality
- **Dynamic Hero Section**: Rotating image slideshow with school highlights
- **Interactive Statistics**: Real-time school performance metrics
- **Academic Programs**: Comprehensive subject offerings and curriculum details
- **Faculty Profiles**: Meet the dedicated teaching staff
- **News & Events**: Latest school updates and announcements
- **Photo Gallery**: Visual showcase of school life and activities
- **Contact Information**: Easy ways to reach the school

### 🚀 Advanced Features
- **AI Search Integration**: Multiple AI-powered search engines for educational resources
- **STEM Resources**: Dedicated section for Science, Technology, Engineering, and Mathematics
- **Students Hub**: Educational resource center with embedded learning tools
- **Donation Portal**: Secure online donation system via Paystack
- **Social Media Integration**: Facebook and WhatsApp connectivity
- **Floating Action Buttons**: Quick access to social media and scroll functionality

## 🛠️ Technology Stack

### Frontend Framework
- **React 18.3.1** - Modern React with hooks and functional components
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and development server

### Styling & UI
- **Tailwind CSS 3.4.1** - Utility-first CSS framework
- **Framer Motion 10.16.4** - Smooth animations and transitions
- **Material-UI (MUI) 5.14.18** - React component library
- **Emotion** - CSS-in-JS styling

### Routing & Navigation
- **React Router DOM 6.18.0** - Client-side routing
- **Dynamic navigation** with mobile-responsive menu

### Icons & Assets
- **Lucide React** - Beautiful, customizable icons
- **Google Fonts** - Inter, Anton, Dancing Script, Playfair Display
- **ImageKit** - Optimized image delivery

### Development Tools
- **ESLint** - Code linting and quality
- **PostCSS & Autoprefixer** - CSS processing
- **TypeScript ESLint** - TypeScript-specific linting

## 📁 Project Structure

```
src/
├── components/
│   ├── common/           # Reusable UI components
│   ├── home/            # Homepage-specific components
│   └── layout/          # Layout components (Header, Footer)
├── pages/               # Page components
├── data/                # Static data and configurations
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
└── styles/              # Global styles
```

### Key Pages
- **HomePage** - Main landing page with hero section and overview
- **AboutPage** - School history, mission, and values
- **AcademicsPage** - Academic programs and curriculum
- **AdmissionsPage** - Admission process, requirements, and application information
- **FacultyPage** - Staff profiles and administration
- **NewsPage** - Latest news and announcements
- **GalleryPage** - Photo gallery of school activities
- **ContactPage** - Contact information and location
- **STEMPage** - STEM resources and programs
- **StudentsHubPage** - Educational resource center
- **AISearchPage** - AI-powered search tools
- **DonationPage** - Secure donation portal

## 🚀 Getting Started

### Prerequisites
- Node.js (version 16 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/stlouisdemojhs.git
   cd stlouisdemojhs
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173` to view the website

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint for code quality

## 🎨 Design Philosophy

### Color Scheme
The website uses the official school colors:
- **Green** - Growth and harmony
- **Blue** - Trust and stability
- **Yellow** - Energy and optimism
- **White** - Purity and clarity

### Typography
- **Headers**: Arial Bold for clean, professional appearance
- **Body Text**: Inter for excellent readability
- **Decorative**: Dancing Script and Playfair Display for special elements

### User Experience
- **Mobile-First**: Designed primarily for mobile users
- **Fast Loading**: Optimized images and lazy loading
- **Accessibility**: Semantic HTML and keyboard navigation
- **Smooth Animations**: Framer Motion for engaging interactions

## 📊 School Statistics (Auto-Updated)

The website features dynamic statistics that automatically update:
- **School Age**: 47+ years (increments annually)
- **Total Graduates**: 30,000+ (increments every 10 years)
- **Current Enrollment**: 800-900+ students
- **BECE Success Rate**: 98%+ consistently

## 🔧 Configuration

### Environment Setup
The project uses Vite for development and build processes. Key configuration files:
- `vite.config.ts` - Vite configuration
- `tailwind.config.js` - Tailwind CSS configuration
- `tsconfig.json` - TypeScript configuration
- `eslint.config.js` - ESLint rules

### Image Optimization
Images are served through ImageKit CDN for optimal performance:
- Automatic format conversion (WebP, AVIF)
- Responsive image delivery
- Lazy loading implementation

## 🌐 Deployment

The website is optimized for deployment on modern hosting platforms:

### Build for Production
```bash
npm run build
```

### Deployment Platforms
- **Netlify** (recommended) - Includes `_redirects` file for SPA routing
- **Vercel** - Zero-configuration deployment
- **GitHub Pages** - Static site hosting

## 🤝 Contributing

We welcome contributions to improve the website! Please follow these guidelines:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style
- Use TypeScript for type safety
- Follow ESLint rules
- Use Tailwind CSS for styling
- Write meaningful commit messages

## 📞 Contact & Support

**St. Louis Demonstration Junior High School**
- **Address**: Kumasi, Ghana
- **Phone**: [School Phone Number]
- **Email**: [School Email]
- **Website**: [Website URL]

### Social Media
- **Facebook**: [@stlouisdemojhs](https://www.facebook.com/stlouisdemojhs)
- **WhatsApp**: [School WhatsApp Channel](https://whatsapp.com/channel/0029VbBO7RD7IUYZjOnapG3q)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **School Administration** - For their vision and support
- **Faculty and Staff** - For their dedication to education
- **Students and Parents** - For their continued trust
- **Development Team** - For bringing this vision to life

---

**Built with ❤️ for St. Louis Demonstration Junior High School**

*Empowering the next generation through excellent education and technology*
