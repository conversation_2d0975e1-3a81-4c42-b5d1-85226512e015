import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import Layout from './components/layout/Layout';
import { HeaderProvider } from './contexts/HeaderContext';
import HomePage from './pages/HomePage';
import AboutPage from './pages/AboutPage';
import AcademicsPage from './pages/AcademicsPage';
import AdmissionsPage from './pages/AdmissionsPage';
import ContactPage from './pages/ContactPage';
import FacultyPage from './pages/FacultyPage';
import DonationPage from './pages/DonationPage';
import NewsPage from './pages/NewsPage';
import CalendarPage from './pages/CalendarPage';
import AISearchPage from './pages/AISearchPage';
import STEMPage from './pages/STEMPage';
import StudentsHubPage from './pages/StudentsHubPage';
import AdviceSpeechesPage from './pages/AdviceSpeechesPage';
import GalleryPage from './pages/GalleryPage';
import ApplyNowPage from './pages/ApplyNowPage';
import ScheduleVisitPage from './pages/ScheduleVisitPage';
import PartnerPage from './pages/PartnerPage';
import AlumniPage from './pages/AlumniPage';
import MediaPage from './pages/MediaPage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import TermsOfServicePage from './pages/TermsOfServicePage';
import SitemapPage from './pages/SitemapPage';
import CharacterEducationPage from './pages/CharacterEducationPage';
import STEMEducationPage from './pages/STEMEducationPage';
import CreativeArtsPage from './pages/CreativeArtsPage';
import LanguageCommunicationPage from './pages/LanguageCommunicationPage';
import CoreAcademicPage from './pages/CoreAcademicPage';
import StaffResourcesPage from './pages/StaffResourcesPage';
import AITeachingGuidePage from './pages/AITeachingGuidePage';
import JHSTextbooksPage from './pages/JHSTextbooksPage';
import DreamHiveResourcesPage from './pages/DreamHiveResourcesPage';
import CareerReelResourcesPage from './pages/CareerReelResourcesPage';
import MoneySmartLinksPage from './pages/MoneySmartLinksPage';
import NotFoundPage from './pages/NotFoundPage';

// Girls Club Pages
import UnderstandingYourBodyPage from './pages/girls-club/UnderstandingYourBodyPage';
import MenstrualHealthPage from './pages/girls-club/MenstrualHealthPage';
import HealthyRelationshipsPage from './pages/girls-club/HealthyRelationshipsPage';
import ChoosingFriendsPage from './pages/girls-club/ChoosingFriendsPage';
import DiscoveringGiftsPage from './pages/girls-club/DiscoveringGiftsPage';
import SexEducationPageGirls from './pages/girls-club/SexEducationPage';
import CareerPlanningPage from './pages/girls-club/CareerPlanningPage';
import PersonalSafetyPage from './pages/girls-club/PersonalSafetyPage';
import SelfConfidencePage from './pages/girls-club/SelfConfidencePage';

// Boys Club Pages
import UnderstandingYourBodyPageBoys from './pages/boys-club/UnderstandingYourBodyPage';
import PersonalHygienePage from './pages/boys-club/PersonalHygienePage';
import BuildingFriendshipsPage from './pages/boys-club/BuildingFriendshipsPage';
import RespectRelationshipsPage from './pages/boys-club/RespectRelationshipsPage';
import SexEducationPageBoys from './pages/boys-club/SexEducationPage';
import DiscoveringStrengthsPage from './pages/boys-club/DiscoveringStrengthsPage';
import CareerExplorationPage from './pages/boys-club/CareerExplorationPage';


// Scroll Position Manager Component
const ScrollPositionManager: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    const saveScrollPosition = () => {
      const scrollPosition = window.scrollY;
      const pathname = window.location.pathname;
      sessionStorage.setItem(`scrollPosition_${pathname}`, scrollPosition.toString());
    };

    const restoreScrollPosition = () => {
      const pathname = location.pathname;
      const savedPosition = sessionStorage.getItem(`scrollPosition_${pathname}`);

      if (savedPosition) {
        // Use requestAnimationFrame to ensure DOM is ready
        requestAnimationFrame(() => {
          window.scrollTo({
            top: parseInt(savedPosition, 10),
            behavior: 'instant'
          });
        });
      } else {
        // Only scroll to top if no saved position (new page visit)
        window.scrollTo(0, 0);
      }
    };

    // Save scroll position before page unload
    const handleBeforeUnload = () => {
      saveScrollPosition();
    };

    // Save scroll position periodically while scrolling
    const handleScroll = () => {
      saveScrollPosition();
    };

    // Restore scroll position on page load
    restoreScrollPosition();

    // Add event listeners
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('scroll', handleScroll);

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [location.pathname]);

  return null;
};

const App: React.FC = () => {
  return (
    <HeaderProvider>
      <Router>
        {/* Scroll Position Manager */}
        <ScrollPositionManager />



        <Routes>
        {/* Main layout with nested routes */}
        <Route path="/" element={<Layout />}>
          <Route index element={<HomePage />} />
          <Route path="about" element={<AboutPage />} />
          <Route path="academics" element={<AcademicsPage />} />
          <Route path="admissions" element={<AdmissionsPage />} />
          <Route path="faculty" element={<FacultyPage />} />
          <Route path="staff-resources" element={<StaffResourcesPage />} />
          <Route path="ai-teaching-guide" element={<AITeachingGuidePage />} />
          <Route path="jhs-textbooks" element={<JHSTextbooksPage />} />
          <Route path="dream-hive-resources" element={<DreamHiveResourcesPage />} />
          <Route path="career-reel-resources" element={<CareerReelResourcesPage />} />
          <Route path="money-smart-links" element={<MoneySmartLinksPage />} />
          <Route path="contact" element={<ContactPage />} />
          <Route path="news" element={<NewsPage />} />
          <Route path="stem" element={<STEMPage />} />
          <Route path="learnhub" element={<StudentsHubPage />} />
          <Route path="students-hub" element={<StudentsHubPage />} />
          <Route path="calendar" element={<CalendarPage />} />
          <Route path="ai-search" element={<AISearchPage />} />
          <Route path="advice-speeches" element={<AdviceSpeechesPage />} />
          <Route path="gallery" element={<GalleryPage />} />
          <Route path="apply-now" element={<ApplyNowPage />} />
          <Route path="schedule-visit" element={<ScheduleVisitPage />} />
          <Route path="partner" element={<PartnerPage />} />
          <Route path="alumni" element={<AlumniPage />} />
          <Route path="media" element={<MediaPage />} />
          <Route path="donate" element={<DonationPage />} />

          {/* Legal Pages */}
          <Route path="privacy-policy" element={<PrivacyPolicyPage />} />
          <Route path="terms-of-service" element={<TermsOfServicePage />} />
          <Route path="sitemap" element={<SitemapPage />} />

          {/* Academic Area Pages */}
          <Route path="character-education" element={<CharacterEducationPage />} />
          <Route path="stem-education" element={<STEMEducationPage />} />
          <Route path="creative-arts" element={<CreativeArtsPage />} />
          <Route path="language-communication" element={<LanguageCommunicationPage />} />
          <Route path="core-academic" element={<CoreAcademicPage />} />

          {/* Girls Club Pages */}
          <Route path="girls-club/understanding-your-body" element={<UnderstandingYourBodyPage />} />
          <Route path="girls-club/menstrual-health" element={<MenstrualHealthPage />} />
          <Route path="girls-club/healthy-relationships" element={<HealthyRelationshipsPage />} />
          <Route path="girls-club/choosing-friends" element={<ChoosingFriendsPage />} />
          <Route path="girls-club/discovering-gifts" element={<DiscoveringGiftsPage />} />
          <Route path="girls-club/sex-education" element={<SexEducationPageGirls />} />
          <Route path="girls-club/career-planning" element={<CareerPlanningPage />} />
          <Route path="girls-club/personal-safety" element={<PersonalSafetyPage />} />
          <Route path="girls-club/self-confidence" element={<SelfConfidencePage />} />

          {/* Boys Club Pages */}
          <Route path="boys-club/understanding-your-body" element={<UnderstandingYourBodyPageBoys />} />
          <Route path="boys-club/personal-hygiene" element={<PersonalHygienePage />} />
          <Route path="boys-club/building-friendships" element={<BuildingFriendshipsPage />} />
          <Route path="boys-club/respect-relationships" element={<RespectRelationshipsPage />} />
          <Route path="boys-club/sex-education" element={<SexEducationPageBoys />} />
          <Route path="boys-club/discovering-strengths" element={<DiscoveringStrengthsPage />} />
          <Route path="boys-club/career-exploration" element={<CareerExplorationPage />} />

          {/* Additional routes will be added here */}

          {/* Catch-all route for paths within the layout */}
          <Route path="*" element={<NotFoundPage />} />
        </Route>

        {/* Global catch-all route for direct access to non-existent routes */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Router>
    </HeaderProvider>
  );
};

export default App;