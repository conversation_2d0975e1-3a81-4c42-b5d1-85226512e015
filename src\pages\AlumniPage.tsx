import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Users, GraduationCap, Heart, Award } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import SectionDivider from '../components/common/SectionDivider';

// Apple-style Stopwatch Timer Component
const AppleTimer: React.FC<{ targetDate: string; eventName: string }> = ({ targetDate, eventName }) => {
  const [timeLeft, setTimeLeft] = useState({
    years: 0,
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    const calculateTimeLeft = () => {
      const difference = new Date(targetDate).getTime() - new Date().getTime();

      if (difference > 0) {
        const years = Math.floor(difference / (1000 * 60 * 60 * 24 * 365));
        const days = Math.floor((difference % (1000 * 60 * 60 * 24 * 365)) / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ years, days, hours, minutes, seconds });
      }
    };

    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [targetDate]);

  const timeUnits = [
    { label: 'Y', value: timeLeft.years, color: 'from-blue-500 to-blue-600' },
    { label: 'D', value: timeLeft.days, color: 'from-green-500 to-green-600' },
    { label: 'H', value: timeLeft.hours, color: 'from-purple-500 to-purple-600' },
    { label: 'M', value: timeLeft.minutes, color: 'from-orange-500 to-orange-600' },
    { label: 'S', value: timeLeft.seconds, color: 'from-red-500 to-red-600' }
  ];

  return (
    <div className="mt-auto">
      <div className="flex items-center justify-center mb-2">
        <span className="text-xs text-white/60 mr-1">⏱</span>
        <span className="text-xs font-medium text-white/60 uppercase tracking-wide">Countdown</span>
      </div>
      <div className="grid grid-cols-5 gap-1">
        {timeUnits.map((unit, index) => (
          <motion.div
            key={unit.label}
            className="text-center"
            animate={{
              scale: unit.label === 'S' ? [1, 1.05, 1] : 1
            }}
            transition={{
              duration: 1,
              repeat: unit.label === 'S' ? Infinity : 0,
              ease: "easeInOut"
            }}
          >
            <div className={`w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br ${unit.color} rounded-lg flex items-center justify-center mb-1 shadow-lg border border-white/20`}>
              <span className="text-white font-mono font-bold text-xs sm:text-sm">
                {unit.value.toString().padStart(2, '0')}
              </span>
            </div>
            <div className="text-white/70 text-xs font-medium uppercase tracking-wider">
              {unit.label}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

// Shimmer Loading Component
const ShimmerLoader: React.FC<{ className?: string; rounded?: string }> = ({
  className = "w-full h-40",
  rounded = "rounded-xl"
}) => (
  <div className={`relative overflow-hidden ${rounded} bg-gray-800 ${className}`}>
    <div className="absolute inset-0 animate-shimmer bg-gradient-to-r from-gray-800 via-gray-600 to-gray-800"></div>
  </div>
);

// Optimized Image Component with Shimmer Loading
const OptimizedImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
  onClick?: () => void;
  shimmerClassName?: string;
}> = ({ src, alt, className, onClick, shimmerClassName }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  return (
    <div className="relative">
      {!isLoaded && !hasError && (
        <ShimmerLoader className={shimmerClassName || className} />
      )}
      <img
        src={src}
        alt={alt}
        className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
        onClick={onClick}
        loading="lazy"
        decoding="async"
        onLoad={() => setIsLoaded(true)}
        onError={() => setHasError(true)}
        style={{ contentVisibility: 'auto' }}
      />
      {hasError && (
        <div className={`${className} bg-gray-800 flex items-center justify-center text-gray-400`}>
          <span>Failed to load image</span>
        </div>
      )}
    </div>
  );
};

const AlumniPage: React.FC = () => {
  const navigate = useNavigate();

  const alumniStats = [
    { icon: <GraduationCap className="w-8 h-8" />, number: "30,000+", label: "Graduates", color: "from-blue-500 to-cyan-500" },
    { icon: <span className="text-2xl">📅</span>, number: "47+", label: "Years of Excellence", color: "from-green-500 to-emerald-500" },
    { icon: <Users className="w-8 h-8" />, number: "10,000+", label: "Active Alumni", color: "from-purple-500 to-pink-500" },
    { icon: <Award className="w-8 h-8" />, number: "100+", label: "Success Stories", color: "from-orange-500 to-red-500" }
  ];

  const featuredAlumni = [
    {
      name: "Emmanuel H. Dwamena",
      class: "Class of 2012",
      profession: "Founder of AIDEL | Author",
      achievement: "Tech entrepreneur, innovator in educational technology, and published author",
      image: "https://ik.imagekit.io/humbling/St%20Louis%20Demo%20Jhs/1718218562009.jpeg?updatedAt=1748301421227",
      quote: "St. Louis Demo JHS instilled in me the entrepreneurial spirit and problem-solving mindset that led to founding AIDEL.",
      linkedin: "https://www.linkedin.com/in/edhumbling/",
      bookUrl: "https://www.amazon.com/Simple-Yet-Great-simplicity-greatness/dp/**********"
    },
    {
      name: "Michael Boateng Duah, MS, MLS(ASCPi)CM",
      class: "Class of 2012",
      profession: "Clinical Laboratory Scientist | Technologist | Author",
      achievement: "Phage Therapy enthusiast advancing medical laboratory science and published author",
      image: "https://ik.imagekit.io/humbling/St%20Louis%20Demo%20Jhs/1741280603621.jpeg?updatedAt=1748301421485",
      quote: "The scientific foundation I received here sparked my passion for laboratory medicine and innovative healthcare solutions.",
      linkedin: "https://www.linkedin.com/in/duahmb/",
      bookUrl: "https://phagesandbiomes.com/en/books"
    },
    {
      name: "Patricia Amankwaah",
      class: "Class of 2012",
      profession: "Registered Nurse",
      achievement: "Dedicated healthcare professional serving the community",
      image: "https://ik.imagekit.io/humbling/St%20Louis%20Demo%20Jhs/************************************100255322_n.jpg?updatedAt=1748301421385",
      quote: "The values of compassion and service I learned here guide my nursing practice every day."
    }
  ];

  const alumniEvents = [
    {
      title: "Grand Voyeur Celebration",
      date: "December 2030",
      description: "A magnificent celebration marking our journey into the future.",
      type: "Future Milestone",
      year: "2030",
      targetDate: "2030-12-15T00:00:00Z"
    },
    {
      title: "Century Convergence",
      date: "June 2045",
      description: "Generations of alumni converge to share wisdom and innovation.",
      type: "Centennial Vision",
      year: "2045",
      targetDate: "2045-06-21T00:00:00Z"
    },
    {
      title: "Arrivals of Posterity",
      date: "September 2060",
      description: "Honoring future generations and our eternal legacy.",
      type: "Legacy Celebration",
      year: "2060",
      targetDate: "2060-09-10T00:00:00Z"
    }
  ];

  return (
    <div className="min-h-screen bg-black">
      {/* Native Back Button - Apple Design */}
      <div className="fixed top-4 left-4 z-50">
        <button
          onClick={() => navigate(-1)}
          className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-black/20 backdrop-blur-md rounded-full border border-white/20 text-white hover:bg-black/30 transition-all duration-200 shadow-lg"
          style={{ backdropFilter: 'blur(20px)' }}
        >
          <ArrowLeft size={16} className="sm:w-[18px] sm:h-[18px]" />
        </button>
      </div>

      {/* Hero Section with School Background - Mobile-Friendly Dark Aero */}
      <section className="py-8 sm:py-12 md:py-16 text-white relative overflow-hidden">
        {/* Optimized School Background Image */}
        <div className="absolute inset-0">
          <OptimizedImage
            src="https://ik.imagekit.io/humbling/St%20Louis%20Demo%20Jhs/IMG_7111.HEIC?updatedAt=1748185709667&tr=w-1200,h-800,q-70"
            alt="St. Louis Demo JHS Alumni Background"
            className="w-full h-full object-cover"
            shimmerClassName="w-full h-full"
          />
        </div>
        {/* Dark Aero Glass Overlay */}
        <div className="absolute inset-0 bg-black/75 backdrop-blur-sm"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/40 via-black/50 to-green-900/40"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(59,130,246,0.15),transparent_70%)]"></div>
        <div className="w-full px-3 sm:px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto text-center"
          >
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-4 sm:mb-6 shadow-xl">
              <Users className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
            </div>
            <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-3 sm:mb-4 leading-tight" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8)' }}>
              Alumni Community
            </h1>
            <p className="text-sm sm:text-base md:text-lg text-gray-100 mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}>
              Celebrating 47+ years of excellence and the remarkable achievements of our 30,000+ graduates
            </p>
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 justify-center">
              <a
                href="#join"
                className="inline-flex items-center justify-center px-3 py-1.5 sm:px-4 sm:py-2 bg-white text-blue-600 text-xs sm:text-sm font-semibold rounded-lg transition-all duration-200 hover:bg-gray-50"
              >
                <Heart className="w-3 h-3 mr-1.5" />
                Join Network
              </a>
              <a
                href="#stories"
                className="inline-flex items-center justify-center px-3 py-1.5 sm:px-4 sm:py-2 bg-white/10 backdrop-blur-sm text-white text-xs sm:text-sm font-semibold rounded-lg border border-white/20 hover:bg-white/20 transition-all duration-200"
              >
                Success Stories
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      <SectionDivider position="bottom" />

      {/* Alumni Stats - Mobile-Friendly Dark Aero */}
      <section className="py-6 sm:py-8 md:py-12 relative overflow-hidden">
        {/* Optimized School Background Image */}
        <div className="absolute inset-0">
          <OptimizedImage
            src="https://ik.imagekit.io/humbling/St%20Louis%20Demo%20Jhs/IMG_7097.HEIC?tr=w-1200,h-800,q-60"
            alt="St. Louis Demo JHS Background"
            className="w-full h-full object-cover opacity-30"
            shimmerClassName="w-full h-full opacity-30"
          />
        </div>
        {/* Dark Aero Glass Overlay */}
        <div className="absolute inset-0 bg-black/80 backdrop-blur-sm"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/40 via-black/60 to-green-900/40"></div>
        <div className="w-full px-3 sm:px-4 relative z-10">
          <div className="grid grid-cols-2 gap-3 sm:gap-4 md:gap-6 max-w-3xl mx-auto">
            {alumniStats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center bg-white/5 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-white/10"
              >
                <div className={`w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br ${stat.color} rounded-xl flex items-center justify-center text-white mx-auto mb-2 sm:mb-3 shadow-lg`}>
                  <div className="scale-75 sm:scale-100">{stat.icon}</div>
                </div>
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-white mb-1 sm:mb-2">{stat.number}</h3>
                <p className="text-gray-300 font-medium text-xs sm:text-sm">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <SectionDivider position="bottom" flip={true} />

      {/* Featured Alumni - Mobile-Friendly Dark Aero */}
      <section id="stories" className="py-6 sm:py-8 md:py-12 relative overflow-hidden">
        {/* Optimized School Background Image */}
        <div className="absolute inset-0">
          <OptimizedImage
            src="https://ik.imagekit.io/humbling/St%20Louis%20Demo%20Jhs/IMG_7111.HEIC?updatedAt=1748185709667&tr=w-1200,h-800,q-70"
            alt="St. Louis Demo JHS Alumni Background"
            className="w-full h-full object-cover"
            shimmerClassName="w-full h-full"
          />
        </div>
        {/* Dark Aero Glass Overlay */}
        <div className="absolute inset-0 bg-black/75 backdrop-blur-sm"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/40 via-black/50 to-green-900/40"></div>
        <div className="w-full px-3 sm:px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto text-center mb-6 sm:mb-8"
          >
            <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-white mb-3 sm:mb-4">
              Success Stories
            </h2>
            <p className="text-sm sm:text-base text-gray-200 max-w-3xl mx-auto">
              Meet some of our distinguished alumni who are making a difference in their communities and professions.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 max-w-7xl mx-auto">
            {featuredAlumni.map((alumni, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="glass-card rounded-lg p-3 sm:p-4 shadow-lg hover:shadow-xl transition-all duration-200 border border-white/20 h-full flex flex-col"
                whileHover={{ y: -2, transition: { duration: 0.2 } }}
              >
                <div className="flex flex-col items-center text-center gap-2 sm:gap-3 flex-1">
                  <div className="w-14 h-14 sm:w-16 sm:h-16 rounded-full overflow-hidden flex-shrink-0">
                    <OptimizedImage
                      src={alumni.image}
                      alt={alumni.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1 flex flex-col">
                    <h3 className="text-sm sm:text-base font-bold text-white mb-1">{alumni.name}</h3>
                    <p className="text-blue-400 font-medium mb-1 text-xs sm:text-sm">{alumni.class}</p>
                    <p className="text-gray-300 font-medium mb-1 text-xs">{alumni.profession}</p>
                    <p className="text-gray-400 text-xs mb-2 flex-1 line-clamp-2">{alumni.achievement}</p>
                    <blockquote className="text-gray-300 text-xs italic border-l-2 border-blue-500 pl-2 mb-3 text-left line-clamp-3">
                      "{alumni.quote}"
                    </blockquote>
                    <div className="flex flex-col gap-1.5 mt-auto">
                      {alumni.linkedin && (
                        <a
                          href={alumni.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center justify-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-all duration-200"
                        >
                          <span className="mr-1">💼</span>
                          LinkedIn
                        </a>
                      )}
                      {alumni.bookUrl && (
                        <a
                          href={alumni.bookUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center justify-center px-2 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs font-medium rounded transition-all duration-200"
                        >
                          <span className="mr-1">📚</span>
                          Author
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Add Your Story Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-6 sm:mt-8 max-w-4xl mx-auto"
          >
            <div className="glass-card rounded-lg p-3 sm:p-4 shadow-lg border border-green-400/30 bg-green-500/10">
              <div className="text-center">
                <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-lg">✨</span>
                </div>
                <h3 className="text-sm sm:text-base font-bold text-white mb-2">
                  Add Your Success Story
                </h3>
                <p className="text-gray-300 text-xs sm:text-sm mb-3 leading-relaxed">
                  Share your achievements and inspire others. Contact Emmanuel via WhatsApp:
                  <span className="font-bold text-green-400 ml-1">0208705290</span>
                </p>
                <a
                  href="https://wa.me/233208705290?text=Hello%20Emmanuel,%20I%20would%20like%20to%20share%20my%20alumni%20success%20story%20for%20the%20St.%20Louis%20Demo%20JHS%20website."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded transition-all duration-200"
                >
                  <svg className="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                  </svg>
                  Share Story
                </a>
                <p className="text-gray-400 text-xs mt-2">
                  Goal: 100s of success stories!
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <SectionDivider position="bottom" />

      {/* Alumni Events - Mobile-Friendly Dark Aero */}
      <section className="py-6 sm:py-8 md:py-12 relative overflow-hidden">
        {/* Optimized School Background Image */}
        <div className="absolute inset-0">
          <OptimizedImage
            src="https://ik.imagekit.io/humbling/St%20Louis%20Demo%20Jhs/IMG_7097.HEIC?tr=w-1200,h-800,q-60"
            alt="St. Louis Demo JHS Background"
            className="w-full h-full object-cover opacity-20"
            shimmerClassName="w-full h-full opacity-20"
          />
        </div>
        {/* Dark Aero Glass Overlay */}
        <div className="absolute inset-0 bg-black/80 backdrop-blur-sm"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/40 via-black/60 to-green-900/40"></div>
        <div className="w-full px-3 sm:px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto text-center mb-6 sm:mb-8"
          >
            <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-white mb-3 sm:mb-4">
              Alumni Events & Programs
            </h2>
            <p className="text-sm sm:text-base text-gray-200 max-w-3xl mx-auto">
              Stay connected with your alma mater through our various alumni programs and events.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-4 sm:gap-6 max-w-4xl mx-auto">
            {alumniEvents.map((event, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="glass-card rounded-xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20 h-full flex flex-col"
              >
                <div className="flex items-center mb-3">
                  <span className="text-base sm:text-lg mr-2">📅</span>
                  <span className="text-xs font-semibold text-blue-400 bg-blue-900/30 px-2 py-1 rounded-full border border-blue-400/30">
                    {event.type}
                  </span>
                </div>
                <h3 className="text-base sm:text-lg font-bold text-white mb-2 leading-tight">{event.title}</h3>
                <p className="text-gray-300 font-medium mb-2 text-xs sm:text-sm">{event.date}</p>
                <p className="text-gray-400 text-xs sm:text-sm mb-4 leading-relaxed flex-grow">{event.description}</p>
                <AppleTimer targetDate={event.targetDate} eventName={event.title} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <SectionDivider position="bottom" flip={true} />

      {/* Join Alumni Network - Mobile-Friendly Dark Aero */}
      <section id="join" className="py-6 sm:py-8 md:py-12 relative overflow-hidden">
        {/* Optimized School Background Image */}
        <div className="absolute inset-0">
          <OptimizedImage
            src="https://ik.imagekit.io/humbling/St%20Louis%20Demo%20Jhs/IMG_7111.HEIC?updatedAt=1748185709667&tr=w-1200,h-800,q-70"
            alt="St. Louis Demo JHS Alumni Background"
            className="w-full h-full object-cover"
            shimmerClassName="w-full h-full"
          />
        </div>
        {/* Dark Aero Glass Overlay */}
        <div className="absolute inset-0 bg-black/75 backdrop-blur-sm"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/40 via-black/50 to-green-900/40"></div>
        <div className="w-full px-3 sm:px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto text-center"
          >
            <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-white mb-4 sm:mb-6">
              Join Our Alumni Network
            </h2>
            <p className="text-sm sm:text-base text-gray-200 mb-6 sm:mb-8 max-w-3xl mx-auto">
              Stay connected with your fellow graduates and continue to be part of the St. Louis Demo JHS family.
            </p>

            <div className="grid grid-cols-1 gap-4 sm:gap-6 mb-6 sm:mb-8">
              <div className="text-center p-4 sm:p-6 glass-card rounded-xl border border-white/20">
                <span className="text-2xl sm:text-3xl block mb-3 sm:mb-4">📧</span>
                <h3 className="text-base sm:text-lg font-bold text-white mb-2">Email Us</h3>
                <p className="text-gray-300 mb-3 sm:mb-4 text-sm sm:text-base"><EMAIL></p>
                <p className="text-xs sm:text-sm text-gray-400">Send us your updated contact information</p>
              </div>
              <div className="text-center p-4 sm:p-6 glass-card rounded-xl border border-white/20">
                <span className="text-2xl sm:text-3xl block mb-3 sm:mb-4">📞</span>
                <h3 className="text-base sm:text-lg font-bold text-white mb-2">Alumni Coordinator</h3>
                <p className="text-gray-300 mb-1 sm:mb-2 font-semibold text-sm sm:text-base">Emmanuel Humbling Dwamena</p>
                <p className="text-gray-300 mb-3 sm:mb-4 text-sm sm:text-base">+233 20 870 5290</p>
                <p className="text-xs sm:text-sm text-gray-400">Speak with our alumni coordinator</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <a
                href="https://whatsapp.com/channel/0029VbBO7RD7IUYZjOnapG3q"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded transition-all duration-200"
              >
                <svg className="w-3 h-3 mr-1.5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.787"/>
                </svg>
                WhatsApp
              </a>
              <a
                href="https://www.facebook.com/stlouisdemojhs/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-all duration-200"
              >
                <svg className="w-3 h-3 mr-1.5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                Facebook
              </a>
              <a
                href="/contact"
                onClick={(e) => {
                  e.preventDefault();
                  navigate('/contact');
                  // Only scroll to top for new page visits, not when returning
                  if (!sessionStorage.getItem(`scrollPosition_/contact`)) {
                    window.scrollTo({ top: 0, behavior: 'instant' });
                  }
                }}
                className="inline-flex items-center justify-center px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white text-xs font-medium rounded transition-all duration-200"
              >
                <Users className="w-3 h-3 mr-1.5" />
                Contact
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default AlumniPage;
