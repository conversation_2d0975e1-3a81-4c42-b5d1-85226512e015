import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Users, Shield, Star, CheckCircle, AlertTriangle, Zap, Target } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const BuildingFriendshipsPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 via-green-600 to-indigo-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                🤝 Building Strong Friendships
              </h1>
              <p className="text-blue-100 mt-2 text-lg drop-shadow-md">
                Learn about loyalty, trust, and building meaningful friendships that last
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
          {/* Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Brotherhood and Friendship</h2>
                <p className="text-gray-600">Building bonds that make you stronger</p>
              </div>
            </div>
            
            <p className="text-gray-700 leading-relaxed mb-6">
              Strong friendships are one of the most valuable things you can have as a young man. Good friends will 
              have your back, challenge you to be better, and share in your adventures and achievements. Learning 
              how to build and maintain solid friendships will serve you throughout your entire life, from school 
              to career to family.
            </p>

            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
              <p className="text-blue-800 font-medium">
                💡 Remember: Real friendship isn't about being popular or having the most friends. It's about 
                having genuine connections with people who respect and support you.
              </p>
            </div>
          </motion.div>

          {/* What Makes a Good Friend */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Star className="w-6 h-6 text-yellow-600" />
              Qualities of a True Friend
            </h3>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold text-gray-800 mb-4 text-green-700">✅ Look for These Qualities</h4>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Loyal:</strong> Has your back and stands up for you when you're not around</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Trustworthy:</strong> Keeps your secrets and is honest with you</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Reliable:</strong> Shows up when they say they will and keeps promises</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Supportive:</strong> Encourages your goals and celebrates your wins</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Fun:</strong> Makes you laugh and enjoys doing things together</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Respectful:</strong> Respects your boundaries and decisions</span>
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-800 mb-4 text-red-700">🚩 Red Flags to Avoid</h4>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Talks behind your back:</strong> Gossips about you to others</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Peer pressure:</strong> Pressures you to do things you don't want to do</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Competitive in a bad way:</strong> Always trying to one-up you</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Unreliable:</strong> Frequently cancels plans or lets you down</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Disrespectful:</strong> Makes fun of you or puts you down</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Gets you in trouble:</strong> Encourages bad decisions or risky behavior</span>
                  </li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Building Brotherhood */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Shield className="w-6 h-6 text-green-600" />
              Building Brotherhood and Loyalty
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                True friendship among guys is built on mutual respect, shared experiences, and having each other's backs. 
                It's about being there for each other through good times and challenges, and growing together as men.
              </p>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="w-8 h-8 text-blue-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Loyalty</h4>
                  <p className="text-gray-600 text-sm">
                    Stand up for your friends, defend them when they're not around, and be someone they can count on.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-green-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Brotherhood</h4>
                  <p className="text-gray-600 text-sm">
                    Create bonds through shared activities, challenges, and experiences that bring you closer together.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Star className="w-8 h-8 text-purple-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Respect</h4>
                  <p className="text-gray-600 text-sm">
                    Respect each other's differences, boundaries, and goals while supporting each other's growth.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Making Friends */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Zap className="w-6 h-6 text-yellow-600" />
              How to Make New Friends
            </h3>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold text-gray-800 mb-4">🎯 Where to Meet Friends</h4>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Join sports teams or athletic clubs</li>
                  <li>• Participate in gaming or hobby groups</li>
                  <li>• Get involved in school clubs or activities</li>
                  <li>• Volunteer for community service projects</li>
                  <li>• Attend youth group or religious activities</li>
                  <li>• Join martial arts or fitness classes</li>
                  <li>• Participate in outdoor activities or camping</li>
                  <li>• Connect through mutual friends</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-800 mb-4">💬 Starting Conversations</h4>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• "Did you see that game last night?"</li>
                  <li>• "What do you think of [shared interest]?"</li>
                  <li>• "Want to team up for this project?"</li>
                  <li>• "That was a great play/move/idea!"</li>
                  <li>• "Have you tried [activity/game/place]?"</li>
                  <li>• "Want to hang out after [event/class]?"</li>
                  <li>• "I'm getting into [hobby], any tips?"</li>
                  <li>• "Want to grab lunch/play basketball?"</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 bg-blue-50 p-6 rounded-xl">
              <h4 className="font-semibold text-gray-800 mb-3">🤝 Building Connections</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Be yourself - don't try to be someone you're not</li>
                  <li>• Show genuine interest in others</li>
                  <li>• Be a good listener</li>
                  <li>• Share common interests and experiences</li>
                </ul>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Be reliable and follow through on plans</li>
                  <li>• Include others in group activities</li>
                  <li>• Be supportive and encouraging</li>
                  <li>• Give friendships time to develop naturally</li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Maintaining Friendships */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Target className="w-6 h-6 text-purple-600" />
              Maintaining Strong Friendships
            </h3>

            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-green-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Regular Connection</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Make time to hang out regularly</li>
                    <li>• Text or call to check in</li>
                    <li>• Remember important events in their lives</li>
                    <li>• Include them in your plans and activities</li>
                    <li>• Be present when you're together</li>
                  </ul>
                </div>

                <div className="bg-blue-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Being Supportive</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Celebrate their successes and achievements</li>
                    <li>• Be there during tough times</li>
                    <li>• Offer help when they need it</li>
                    <li>• Listen without always trying to fix things</li>
                    <li>• Encourage them to pursue their goals</li>
                  </ul>
                </div>
              </div>

              <div className="bg-yellow-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">Handling Conflicts</h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Address issues directly and honestly</li>
                    <li>• Listen to their perspective</li>
                    <li>• Apologize when you're wrong</li>
                    <li>• Focus on solving the problem, not winning</li>
                  </ul>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Give each other space when needed</li>
                    <li>• Forgive and move forward</li>
                    <li>• Learn from disagreements</li>
                    <li>• Don't let small issues become big problems</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Being a Good Friend */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-gradient-to-r from-blue-500 to-green-600 rounded-2xl shadow-lg p-8 text-white"
          >
            <h3 className="text-2xl font-bold mb-6 flex items-center gap-3">
              <Users className="w-6 h-6" />
              Being the Friend You Want to Have
            </h3>

            <div className="space-y-4">
              <p className="leading-relaxed">
                The best way to have good friends is to be a good friend yourself. Focus on developing the qualities 
                you value in others, and you'll naturally attract people who share your values and character.
              </p>

              <div className="bg-white/20 backdrop-blur-sm rounded-xl p-6">
                <h4 className="font-semibold mb-3">Ways to Be an Awesome Friend:</h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <ul className="space-y-2 text-sm">
                    <li>• Keep your word and be reliable</li>
                    <li>• Be loyal and have their back</li>
                    <li>• Listen without judgment</li>
                    <li>• Celebrate their wins genuinely</li>
                  </ul>
                  <ul className="space-y-2 text-sm">
                    <li>• Support them through challenges</li>
                    <li>• Be honest but kind</li>
                    <li>• Include others and be welcoming</li>
                    <li>• Challenge them to be their best</li>
                  </ul>
                </div>
              </div>

              <p className="text-blue-100 text-sm">
                Remember: Real friendship is about mutual respect, loyalty, and helping each other become better men!
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default BuildingFriendshipsPage;
