import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Briefcase, Target, Rocket, Users, CheckCircle, Star, Award } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const CareerExplorationPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                🚀 Career Exploration & Goals
              </h1>
              <p className="text-indigo-100 mt-2 text-lg drop-shadow-md">
                Discover career paths, set ambitious goals, and build the foundation for your professional success
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-indigo-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-indigo-400/30">
              <Briefcase className="w-6 h-6 text-indigo-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">Your Career Journey Starts Now</h2>
              <p className="text-gray-300">Building the foundation for professional success</p>
            </div>
          </div>
          
          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            Your career isn't just about making money - it's about finding meaningful work that uses your talents, 
            contributes to society, and helps you build the life you want. Starting your career exploration now 
            gives you a huge advantage in making informed decisions about your education, skills development, 
            and future opportunities.
          </p>

          <div className="bg-indigo-500/10 backdrop-blur-sm border-l-4 border-indigo-400 p-4 rounded-r-lg border border-indigo-400/20">
            <p className="text-indigo-200 font-medium drop-shadow-sm">
              💡 Remember: You don't have to choose just one career path. Many successful men have multiple careers 
              throughout their lives, building on their experiences and evolving interests.
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Career Fields */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Target className="w-6 h-6 text-blue-400" />
            Exploring Career Fields
          </h3>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">💻 Technology & Engineering</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Software development and programming</li>
                  <li>• Cybersecurity and data protection</li>
                  <li>• Mechanical, electrical, and civil engineering</li>
                  <li>• Robotics and artificial intelligence</li>
                  <li>• Game development and digital media</li>
                </ul>
              </div>

              <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🏢 Business & Entrepreneurship</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Starting and running your own business</li>
                  <li>• Finance, banking, and investment</li>
                  <li>• Marketing and sales</li>
                  <li>• Management and leadership roles</li>
                  <li>• Consulting and business strategy</li>
                </ul>
              </div>

              <div className="bg-red-500/10 backdrop-blur-sm rounded-xl p-6 border border-red-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🏥 Healthcare & Science</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Medicine and surgery</li>
                  <li>• Research and laboratory sciences</li>
                  <li>• Physical therapy and sports medicine</li>
                  <li>• Veterinary medicine</li>
                  <li>• Biotechnology and pharmaceuticals</li>
                </ul>
              </div>

              <div className="bg-orange-500/10 backdrop-blur-sm rounded-xl p-6 border border-orange-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🔧 Skilled Trades</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Electrician and electrical work</li>
                  <li>• Plumbing and HVAC systems</li>
                  <li>• Construction and carpentry</li>
                  <li>• Automotive repair and mechanics</li>
                  <li>• Welding and metalworking</li>
                </ul>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🎯 Public Service & Safety</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Law enforcement and criminal justice</li>
                  <li>• Military service and defense</li>
                  <li>• Firefighting and emergency services</li>
                  <li>• Government and public administration</li>
                  <li>• Teaching and education</li>
                </ul>
              </div>

              <div className="bg-yellow-500/10 backdrop-blur-sm rounded-xl p-6 border border-yellow-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🎨 Creative & Media</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Film and video production</li>
                  <li>• Graphic design and advertising</li>
                  <li>• Music production and performance</li>
                  <li>• Writing and journalism</li>
                  <li>• Architecture and design</li>
                </ul>
              </div>

              <div className="bg-teal-500/10 backdrop-blur-sm rounded-xl p-6 border border-teal-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">⚖️ Law & Justice</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Attorney and legal practice</li>
                  <li>• Judge and court system</li>
                  <li>• Paralegal and legal support</li>
                  <li>• Corporate law and compliance</li>
                  <li>• International law and diplomacy</li>
                </ul>
              </div>

              <div className="bg-pink-500/10 backdrop-blur-sm rounded-xl p-6 border border-pink-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🌍 Emerging Fields</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Renewable energy and sustainability</li>
                  <li>• Space exploration and aerospace</li>
                  <li>• Virtual reality and augmented reality</li>
                  <li>• Drone technology and automation</li>
                  <li>• Cryptocurrency and blockchain</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Goal Setting */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Rocket className="w-6 h-6 text-green-400" />
            Setting Career Goals
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              Setting clear, achievable goals is crucial for career success. Goals give you direction, motivation, 
              and a way to measure your progress. Start with big dreams, then break them down into actionable steps.
            </p>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-green-400/30">
                  <Target className="w-8 h-8 text-green-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Short-term Goals</h4>
                <p className="text-gray-200 text-sm">
                  Goals for the next 1-2 years: grades, skills, experiences, and activities.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-400/30">
                  <Award className="w-8 h-8 text-blue-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Medium-term Goals</h4>
                <p className="text-gray-200 text-sm">
                  Goals for the next 3-5 years: education, training, internships, and early career steps.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-purple-400/30">
                  <Star className="w-8 h-8 text-purple-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Long-term Vision</h4>
                <p className="text-gray-200 text-sm">
                  Your ultimate career vision: where you want to be in 10-20 years.
                </p>
              </div>
            </div>

            <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🎯 SMART Goal Framework</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• <strong className="text-white">Specific:</strong> Clear and well-defined</li>
                  <li>• <strong className="text-white">Measurable:</strong> You can track progress</li>
                  <li>• <strong className="text-white">Achievable:</strong> Realistic and attainable</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• <strong className="text-white">Relevant:</strong> Aligned with your values and interests</li>
                  <li>• <strong className="text-white">Time-bound:</strong> Has a clear deadline</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Building Your Foundation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Award className="w-6 h-6 text-orange-400" />
            Building Your Career Foundation
          </h3>

          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-orange-500/10 backdrop-blur-sm rounded-xl p-6 border border-orange-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">📚 Education & Skills</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Excel in your current studies</li>
                  <li>• Take challenging courses in areas of interest</li>
                  <li>• Learn technical skills relevant to your field</li>
                  <li>• Develop strong communication and leadership skills</li>
                  <li>• Consider college, trade school, or certification programs</li>
                </ul>
              </div>

              <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🤝 Experience & Networking</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Seek internships and job shadowing opportunities</li>
                  <li>• Volunteer in areas related to your interests</li>
                  <li>• Join clubs and organizations in your field</li>
                  <li>• Attend career fairs and professional events</li>
                  <li>• Connect with professionals on LinkedIn</li>
                </ul>
              </div>
            </div>

            <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">💼 Professional Development</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Build a professional online presence</li>
                  <li>• Create a portfolio of your work and achievements</li>
                  <li>• Practice interviewing and professional communication</li>
                  <li>• Learn about workplace culture and expectations</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Develop a personal brand and reputation</li>
                  <li>• Stay updated on industry trends and changes</li>
                  <li>• Find mentors in your areas of interest</li>
                  <li>• Start building your professional network early</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Action Plan */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gradient-to-r from-indigo-600/80 to-purple-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-indigo-400/30"
        >
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
            <Users className="w-6 h-6" />
            Your Career Exploration Action Plan
          </h3>

          <div className="space-y-4">
            <p className="leading-relaxed drop-shadow-sm">
              Your career journey is unique to you. Start exploring now, stay curious, work hard, and be open to 
              opportunities. The decisions you make today will shape your future success and fulfillment!
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold mb-3 drop-shadow-md">This Month, I Will:</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-sm">
                  <li>• Research three careers that interest me</li>
                  <li>• Interview someone working in my field of interest</li>
                  <li>• Set three specific career-related goals</li>
                  <li>• Explore education and training requirements</li>
                  <li>• Join a club or activity related to my interests</li>
                </ul>
                <ul className="space-y-2 text-sm">
                  <li>• Start building my professional skills</li>
                  <li>• Look for volunteer or internship opportunities</li>
                  <li>• Create a plan for my high school courses</li>
                  <li>• Connect with a potential mentor</li>
                  <li>• Begin building my professional network</li>
                </ul>
              </div>
            </div>

            <p className="text-indigo-100 text-sm drop-shadow-sm">
              Remember: Success comes to those who prepare, work hard, and never stop learning. Your future is 
              in your hands - make it amazing! 🚀
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default CareerExplorationPage;
