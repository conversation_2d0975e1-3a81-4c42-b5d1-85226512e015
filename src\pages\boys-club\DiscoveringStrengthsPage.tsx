import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Zap, Target, Award, Users, CheckCircle, Star, Lightbulb } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const DiscoveringStrengthsPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 via-green-600 to-teal-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                ⚡ Discovering Your Strengths
              </h1>
              <p className="text-blue-100 mt-2 text-lg drop-shadow-md">
                Identify your unique abilities, build on your talents, and develop the confidence to succeed
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-blue-400/30">
              <Zap className="w-6 h-6 text-blue-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">Unleash Your Potential</h2>
              <p className="text-gray-300">Every man has unique strengths waiting to be discovered</p>
            </div>
          </div>
          
          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            As a young man, discovering your strengths is one of the most important things you can do for your future. 
            Your strengths aren't just what you're naturally good at - they're also the areas where you have the 
            potential to excel with effort and practice. Understanding your strengths helps you make better decisions 
            about your education, career, and life goals.
          </p>

          <div className="bg-blue-500/10 backdrop-blur-sm border-l-4 border-blue-400 p-4 rounded-r-lg border border-blue-400/20">
            <p className="text-blue-200 font-medium drop-shadow-sm">
              💡 Remember: Strength isn't just about physical power - it includes mental abilities, emotional intelligence, 
              creativity, leadership skills, and character traits that make you who you are.
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Types of Strengths */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Target className="w-6 h-6 text-green-400" />
            Different Types of Strengths
          </h3>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">💪 Physical Strengths</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Athletic ability and coordination</li>
                  <li>• Endurance and stamina</li>
                  <li>• Hand-eye coordination</li>
                  <li>• Physical strength and agility</li>
                  <li>• Quick reflexes and reaction time</li>
                </ul>
              </div>

              <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🧠 Mental Strengths</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Problem-solving and critical thinking</li>
                  <li>• Memory and learning ability</li>
                  <li>• Mathematical and analytical skills</li>
                  <li>• Focus and concentration</li>
                  <li>• Strategic thinking and planning</li>
                </ul>
              </div>

              <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🎨 Creative Strengths</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Artistic and design abilities</li>
                  <li>• Musical talent and rhythm</li>
                  <li>• Creative writing and storytelling</li>
                  <li>• Innovation and original thinking</li>
                  <li>• Visual and spatial intelligence</li>
                </ul>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-orange-500/10 backdrop-blur-sm rounded-xl p-6 border border-orange-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">👥 Social Strengths</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Leadership and influence</li>
                  <li>• Communication and public speaking</li>
                  <li>• Empathy and understanding others</li>
                  <li>• Teamwork and collaboration</li>
                  <li>• Conflict resolution and mediation</li>
                </ul>
              </div>

              <div className="bg-yellow-500/10 backdrop-blur-sm rounded-xl p-6 border border-yellow-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🔧 Technical Strengths</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Technology and computer skills</li>
                  <li>• Building and mechanical abilities</li>
                  <li>• Engineering and design thinking</li>
                  <li>• Troubleshooting and repair skills</li>
                  <li>• Understanding how things work</li>
                </ul>
              </div>

              <div className="bg-red-500/10 backdrop-blur-sm rounded-xl p-6 border border-red-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">💎 Character Strengths</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Integrity and honesty</li>
                  <li>• Perseverance and determination</li>
                  <li>• Courage and bravery</li>
                  <li>• Loyalty and reliability</li>
                  <li>• Compassion and kindness</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Discovering Your Strengths */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Lightbulb className="w-6 h-6 text-yellow-400" />
            How to Identify Your Strengths
          </h3>

          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-yellow-500/10 backdrop-blur-sm rounded-xl p-6 border border-yellow-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🤔 Self-Reflection Questions</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• What activities make you feel energized and excited?</li>
                  <li>• What do people often ask for your help with?</li>
                  <li>• What comes naturally to you without much effort?</li>
                  <li>• What would you do if you knew you couldn't fail?</li>
                  <li>• What achievements are you most proud of?</li>
                  <li>• When do you feel most confident and capable?</li>
                </ul>
              </div>

              <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🔍 Exploration Activities</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Try different sports, clubs, and activities</li>
                  <li>• Take on leadership roles in group projects</li>
                  <li>• Volunteer for causes you care about</li>
                  <li>• Ask family and friends what they see as your strengths</li>
                  <li>• Keep a journal of your successes and achievements</li>
                  <li>• Take strengths assessments and personality tests</li>
                </ul>
              </div>
            </div>

            <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">📊 Create Your Strengths Profile</h4>
              <div className="grid md:grid-cols-3 gap-4">
                <div>
                  <h5 className="font-medium text-white mb-2">Natural Talents</h5>
                  <p className="text-gray-200 text-sm">Things you're naturally good at without much training</p>
                </div>
                <div>
                  <h5 className="font-medium text-white mb-2">Developed Skills</h5>
                  <p className="text-gray-200 text-sm">Abilities you've built through practice and learning</p>
                </div>
                <div>
                  <h5 className="font-medium text-white mb-2">Growth Areas</h5>
                  <p className="text-gray-200 text-sm">Strengths you want to develop further</p>
                </div>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Building on Strengths */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Award className="w-6 h-6 text-purple-400" />
            Developing and Using Your Strengths
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              Identifying your strengths is just the beginning. The real power comes from developing these strengths 
              and learning how to use them effectively in different areas of your life.
            </p>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-purple-400/30">
                  <Target className="w-8 h-8 text-purple-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Practice Deliberately</h4>
                <p className="text-gray-200 text-sm">
                  Focus on improving your strengths through consistent, purposeful practice and training.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-green-400/30">
                  <Users className="w-8 h-8 text-green-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Find Mentors</h4>
                <p className="text-gray-200 text-sm">
                  Connect with people who excel in your areas of strength and learn from their experience.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-400/30">
                  <Star className="w-8 h-8 text-blue-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Apply Your Strengths</h4>
                <p className="text-gray-200 text-sm">
                  Look for opportunities to use your strengths in school, sports, work, and relationships.
                </p>
              </div>
            </div>

            <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">💪 Strength Development Strategies</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Set specific goals for improving your strengths</li>
                  <li>• Seek feedback from coaches, teachers, and mentors</li>
                  <li>• Join teams, clubs, or groups related to your strengths</li>
                  <li>• Take on challenging projects that stretch your abilities</li>
                  <li>• Study and learn from others who excel in your areas</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Practice regularly and consistently</li>
                  <li>• Compete or perform to test your abilities</li>
                  <li>• Teach others to deepen your own understanding</li>
                  <li>• Combine your strengths in unique ways</li>
                  <li>• Stay curious and keep learning new techniques</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Strengths in Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gradient-to-r from-blue-600/80 to-green-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-blue-400/30"
        >
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
            <Zap className="w-6 h-6" />
            Your Strengths Action Plan
          </h3>

          <div className="space-y-4">
            <p className="leading-relaxed drop-shadow-sm">
              You have incredible potential waiting to be unleashed. Start identifying and developing your strengths 
              today, and watch as doors of opportunity open for your future. Remember, the world needs what you have to offer!
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold mb-3 drop-shadow-md">This Week, I Will:</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-sm">
                  <li>• Identify three of my strongest abilities</li>
                  <li>• Ask three people what they see as my strengths</li>
                  <li>• Try one new activity to explore potential strengths</li>
                  <li>• Set a goal to improve in one area of strength</li>
                  <li>• Look for ways to use my strengths to help others</li>
                </ul>
                <ul className="space-y-2 text-sm">
                  <li>• Research careers that match my strengths</li>
                  <li>• Find a mentor or role model in my area of interest</li>
                  <li>• Practice my strengths for at least 30 minutes daily</li>
                  <li>• Join a club or team related to my strengths</li>
                  <li>• Celebrate my unique combination of abilities</li>
                </ul>
              </div>
            </div>

            <p className="text-blue-100 text-sm drop-shadow-sm">
              Remember: Your strengths are your superpowers. Develop them, use them wisely, and become the strong, 
              capable man you're meant to be! ⚡
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default DiscoveringStrengthsPage;
