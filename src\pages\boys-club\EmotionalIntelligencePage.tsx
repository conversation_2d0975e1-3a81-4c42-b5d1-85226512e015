import React from 'react';
import { motion } from 'framer-motion';
import { Arrow<PERSON>eft, Brain, Heart, Users, Shield, CheckCircle, Star, Target } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const EmotionalIntelligencePage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 via-teal-600 to-green-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                🧠 Emotional Intelligence & Mental Health
              </h1>
              <p className="text-blue-100 mt-2 text-lg drop-shadow-md">
                Build emotional strength, understand your feelings, and maintain good mental health as a young man
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-blue-400/30">
              <Brain className="w-6 h-6 text-blue-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">Emotional Strength for Men</h2>
              <p className="text-gray-300">Real strength includes understanding and managing your emotions</p>
            </div>
          </div>
          
          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            Emotional intelligence isn't about being "soft" - it's about being strong enough to understand your feelings, 
            wise enough to manage them well, and confident enough to show empathy to others. As a young man, developing 
            emotional intelligence will make you a better leader, friend, partner, and person. It's one of the most 
            important skills you can develop for success in all areas of life.
          </p>

          <div className="bg-blue-500/10 backdrop-blur-sm border-l-4 border-blue-400 p-4 rounded-r-lg border border-blue-400/20">
            <p className="text-blue-200 font-medium drop-shadow-sm">
              💡 Remember: The strongest men are those who can understand their emotions, express them appropriately, 
              and help others do the same. This is true masculine strength.
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Breaking Stereotypes */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Shield className="w-6 h-6 text-green-400" />
            Breaking Harmful Stereotypes
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              Society sometimes teaches harmful messages about how men should handle emotions. Let's break down these 
              myths and understand what real emotional strength looks like.
            </p>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-red-500/10 backdrop-blur-sm rounded-xl p-6 border border-red-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md text-red-400">❌ Harmful Myths</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• "Real men don't cry or show emotions"</li>
                  <li>• "Anger is the only acceptable emotion for men"</li>
                  <li>• "Asking for help is a sign of weakness"</li>
                  <li>• "Men should always be tough and stoic"</li>
                  <li>• "Talking about feelings is feminine"</li>
                  <li>• "Men should handle everything alone"</li>
                </ul>
              </div>

              <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md text-green-400">✅ Healthy Truths</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Real men feel and express all emotions appropriately</li>
                  <li>• Emotional awareness leads to better decision-making</li>
                  <li>• Seeking help shows wisdom and strength</li>
                  <li>• Vulnerability builds deeper connections</li>
                  <li>• Emotional intelligence is a leadership skill</li>
                  <li>• Strong men support others emotionally</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Understanding Emotions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Heart className="w-6 h-6 text-red-400" />
            Understanding and Managing Your Emotions
          </h3>

          <div className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-400/30">
                  <Target className="w-8 h-8 text-blue-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Recognize</h4>
                <p className="text-gray-200 text-sm">
                  Learn to identify what you're feeling and why. Name your emotions specifically.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-yellow-400/30">
                  <Brain className="w-8 h-8 text-yellow-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Understand</h4>
                <p className="text-gray-200 text-sm">
                  Explore what triggered the emotion and what it's telling you about your needs.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-green-400/30">
                  <CheckCircle className="w-8 h-8 text-green-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Respond</h4>
                <p className="text-gray-200 text-sm">
                  Choose how to express or act on the emotion in a healthy, constructive way.
                </p>
              </div>
            </div>

            <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">💪 Healthy Ways to Handle Difficult Emotions</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• <strong className="text-white">Anger:</strong> Take deep breaths, exercise, talk it out</li>
                  <li>• <strong className="text-white">Sadness:</strong> Allow yourself to feel it, seek support</li>
                  <li>• <strong className="text-white">Anxiety:</strong> Practice mindfulness, break problems down</li>
                  <li>• <strong className="text-white">Frustration:</strong> Step back, find solutions, ask for help</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• <strong className="text-white">Fear:</strong> Face it gradually, prepare and plan</li>
                  <li>• <strong className="text-white">Loneliness:</strong> Reach out to friends, join activities</li>
                  <li>• <strong className="text-white">Stress:</strong> Organize priorities, practice self-care</li>
                  <li>• <strong className="text-white">Disappointment:</strong> Learn from it, adjust expectations</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Mental Health for Men */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Shield className="w-6 h-6 text-purple-400" />
            Mental Health Awareness for Young Men
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              Mental health challenges affect men just as much as women, but men are often less likely to seek help. 
              Understanding mental health and knowing when to get support is crucial for your well-being and success.
            </p>

            <div className="bg-orange-500/10 backdrop-blur-sm rounded-xl p-6 border border-orange-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🚨 Warning Signs to Watch For</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Persistent anger or irritability</li>
                  <li>• Withdrawing from friends and activities</li>
                  <li>• Changes in sleep or eating patterns</li>
                  <li>• Difficulty concentrating or making decisions</li>
                  <li>• Feeling hopeless or worthless</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Increased risk-taking behavior</li>
                  <li>• Substance use to cope with problems</li>
                  <li>• Physical symptoms without clear cause</li>
                  <li>• Thoughts of self-harm or suicide</li>
                  <li>• Loss of interest in things you used to enjoy</li>
                </ul>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">💚 Building Mental Strength</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Develop a strong support network</li>
                  <li>• Practice stress management techniques</li>
                  <li>• Maintain physical health through exercise</li>
                  <li>• Set realistic goals and expectations</li>
                  <li>• Learn healthy coping strategies</li>
                  <li>• Practice mindfulness and self-reflection</li>
                </ul>
              </div>

              <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🆘 Getting Help</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Talk to trusted family members or friends</li>
                  <li>• Reach out to school counselors</li>
                  <li>• Contact your healthcare provider</li>
                  <li>• Call mental health helplines</li>
                  <li>• Consider therapy or counseling</li>
                  <li>• Remember: seeking help is courageous</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Building Emotional Intelligence */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gradient-to-r from-blue-600/80 to-teal-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-blue-400/30"
        >
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
            <Users className="w-6 h-6" />
            Building Your Emotional Intelligence
          </h3>

          <div className="space-y-4">
            <p className="leading-relaxed drop-shadow-sm">
              Emotional intelligence is a skill that will serve you in every area of life - from relationships and 
              friendships to leadership and career success. Start building yours today and become the strong, 
              emotionally intelligent man the world needs.
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold mb-3 drop-shadow-md">Daily Practices for Emotional Strength:</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-sm">
                  <li>• Check in with your emotions regularly</li>
                  <li>• Practice expressing feelings appropriately</li>
                  <li>• Listen actively to others without judgment</li>
                  <li>• Ask friends how they're really doing</li>
                  <li>• Practice empathy and perspective-taking</li>
                </ul>
                <ul className="space-y-2 text-sm">
                  <li>• Learn stress management techniques</li>
                  <li>• Build and maintain strong friendships</li>
                  <li>• Seek feedback on your emotional responses</li>
                  <li>• Read about emotions and mental health</li>
                  <li>• Model emotional intelligence for other guys</li>
                </ul>
              </div>
            </div>

            <p className="text-blue-100 text-sm drop-shadow-sm">
              Remember: The strongest men are those who understand themselves and others. Emotional intelligence 
              is your superpower - use it to build a better world! 💪🧠
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default EmotionalIntelligencePage;
