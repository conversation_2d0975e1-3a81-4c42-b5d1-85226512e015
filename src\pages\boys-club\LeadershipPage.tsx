import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Crown, Users, Target, Star, CheckCircle, Award, Shield } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const LeadershipPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                👑 Leadership & Responsibility
              </h1>
              <p className="text-purple-100 mt-2 text-lg drop-shadow-md">
                Develop leadership skills, take responsibility, and become a positive influence in your community
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-purple-400/30">
              <Crown className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">Leading with Purpose</h2>
              <p className="text-gray-300">True leadership starts with taking responsibility for yourself</p>
            </div>
          </div>
          
          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            Leadership isn't about having power over others - it's about having the power to inspire, serve, and 
            make a positive difference. As a young man, developing leadership skills and taking responsibility for 
            your actions, choices, and impact on others will set you apart and help you become the kind of man 
            others look up to and trust.
          </p>

          <div className="bg-purple-500/10 backdrop-blur-sm border-l-4 border-purple-400 p-4 rounded-r-lg border border-purple-400/20">
            <p className="text-purple-200 font-medium drop-shadow-sm">
              💡 Remember: Leadership is not about being the loudest or most popular person in the room. 
              It's about being the most responsible, trustworthy, and service-oriented.
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Types of Leadership */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Star className="w-6 h-6 text-yellow-400" />
            Different Types of Leadership
          </h3>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🎯 Leading by Example</h4>
                <p className="text-gray-200 text-sm mb-3">The most powerful form of leadership</p>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Demonstrating the behavior you want to see</li>
                  <li>• Being consistent in your values and actions</li>
                  <li>• Taking responsibility for your mistakes</li>
                  <li>• Showing integrity even when no one is watching</li>
                  <li>• Inspiring others through your character</li>
                </ul>
              </div>

              <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🤝 Servant Leadership</h4>
                <p className="text-gray-200 text-sm mb-3">Leading by serving others first</p>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Putting the needs of others before your own</li>
                  <li>• Helping team members grow and succeed</li>
                  <li>• Listening to and valuing others' input</li>
                  <li>• Building up rather than tearing down</li>
                  <li>• Creating opportunities for others to shine</li>
                </ul>
              </div>

              <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🚀 Visionary Leadership</h4>
                <p className="text-gray-200 text-sm mb-3">Inspiring others toward a common goal</p>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Seeing possibilities that others might miss</li>
                  <li>• Communicating a compelling vision</li>
                  <li>• Motivating others to work toward shared goals</li>
                  <li>• Staying focused on the bigger picture</li>
                  <li>• Encouraging innovation and creativity</li>
                </ul>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-orange-500/10 backdrop-blur-sm rounded-xl p-6 border border-orange-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">💪 Quiet Leadership</h4>
                <p className="text-gray-200 text-sm mb-3">Leading without seeking attention</p>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Working hard behind the scenes</li>
                  <li>• Supporting others without expecting recognition</li>
                  <li>• Being reliable and dependable</li>
                  <li>• Solving problems before they become big issues</li>
                  <li>• Leading through competence and character</li>
                </ul>
              </div>

              <div className="bg-red-500/10 backdrop-blur-sm rounded-xl p-6 border border-red-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🛡️ Protective Leadership</h4>
                <p className="text-gray-200 text-sm mb-3">Standing up for what's right</p>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Defending those who can't defend themselves</li>
                  <li>• Speaking up against injustice or bullying</li>
                  <li>• Creating safe spaces for others</li>
                  <li>• Taking stands on important issues</li>
                  <li>• Using your strength to protect, not harm</li>
                </ul>
              </div>

              <div className="bg-teal-500/10 backdrop-blur-sm rounded-xl p-6 border border-teal-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🌟 Collaborative Leadership</h4>
                <p className="text-gray-200 text-sm mb-3">Leading through teamwork</p>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Bringing people together toward common goals</li>
                  <li>• Facilitating group discussions and decisions</li>
                  <li>• Recognizing and utilizing everyone's strengths</li>
                  <li>• Building consensus and unity</li>
                  <li>• Sharing credit and taking responsibility</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Taking Responsibility */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Shield className="w-6 h-6 text-green-400" />
            Taking Personal Responsibility
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              Personal responsibility is the foundation of all leadership. It means owning your choices, learning 
              from your mistakes, and taking action to improve yourself and your situation. Responsible men don't 
              make excuses - they make changes.
            </p>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-green-400/30">
                  <Target className="w-8 h-8 text-green-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Own Your Actions</h4>
                <p className="text-gray-200 text-sm">
                  Take full responsibility for your choices and their consequences, both good and bad.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-400/30">
                  <CheckCircle className="w-8 h-8 text-blue-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Learn from Mistakes</h4>
                <p className="text-gray-200 text-sm">
                  View failures as learning opportunities and use them to grow stronger and wiser.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-purple-400/30">
                  <Award className="w-8 h-8 text-purple-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Take Initiative</h4>
                <p className="text-gray-200 text-sm">
                  Don't wait for others to solve problems - step up and take action when needed.
                </p>
              </div>
            </div>

            <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🎯 Areas of Personal Responsibility</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• <strong className="text-white">Academic Performance:</strong> Your grades and effort in school</li>
                  <li>• <strong className="text-white">Physical Health:</strong> Exercise, nutrition, and self-care</li>
                  <li>• <strong className="text-white">Relationships:</strong> How you treat family, friends, and others</li>
                  <li>• <strong className="text-white">Character:</strong> Your integrity, honesty, and values</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• <strong className="text-white">Future Planning:</strong> Setting and working toward goals</li>
                  <li>• <strong className="text-white">Community Impact:</strong> How you contribute to society</li>
                  <li>• <strong className="text-white">Personal Growth:</strong> Continuously improving yourself</li>
                  <li>• <strong className="text-white">Emotional Regulation:</strong> Managing your reactions and responses</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Leadership in Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Users className="w-6 h-6 text-blue-400" />
            Leadership Opportunities for Young Men
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              You don't need a title or position to be a leader. Leadership opportunities are all around you, 
              waiting for someone with the courage and character to step up and make a difference.
            </p>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🏫 At School</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Run for student government or class representative</li>
                  <li>• Start or join clubs that interest you</li>
                  <li>• Organize study groups or tutoring sessions</li>
                  <li>• Stand up against bullying and exclusion</li>
                  <li>• Help new students feel welcome</li>
                  <li>• Lead by example in academic integrity</li>
                </ul>
              </div>

              <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🏠 At Home</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Take initiative with household responsibilities</li>
                  <li>• Be a positive role model for younger siblings</li>
                  <li>• Help resolve family conflicts peacefully</li>
                  <li>• Show respect and appreciation to parents</li>
                  <li>• Take care of family pets or property</li>
                  <li>• Contribute to family decisions appropriately</li>
                </ul>
              </div>

              <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🌍 In the Community</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Volunteer for local charities or causes</li>
                  <li>• Organize community service projects</li>
                  <li>• Mentor younger kids in sports or activities</li>
                  <li>• Participate in environmental cleanup efforts</li>
                  <li>• Help elderly neighbors with tasks</li>
                  <li>• Advocate for issues you care about</li>
                </ul>
              </div>

              <div className="bg-orange-500/10 backdrop-blur-sm rounded-xl p-6 border border-orange-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">⚽ In Sports & Activities</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Be a team captain or co-captain</li>
                  <li>• Encourage teammates and build team spirit</li>
                  <li>• Help coach younger players</li>
                  <li>• Demonstrate good sportsmanship</li>
                  <li>• Organize team bonding activities</li>
                  <li>• Support teammates through challenges</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Leadership Action Plan */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gradient-to-r from-purple-600/80 to-blue-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-purple-400/30"
        >
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
            <Crown className="w-6 h-6" />
            Your Leadership Development Plan
          </h3>

          <div className="space-y-4">
            <p className="leading-relaxed drop-shadow-sm">
              Leadership is not about being perfect - it's about being willing to grow, serve, and make a positive 
              impact. Start where you are, use what you have, and do what you can. The world needs your unique 
              leadership style and perspective.
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold mb-3 drop-shadow-md">This Month, I Will:</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-sm">
                  <li>• Identify one area where I can show leadership</li>
                  <li>• Take responsibility for a mistake I've made</li>
                  <li>• Help someone who needs support</li>
                  <li>• Stand up for someone being treated unfairly</li>
                  <li>• Volunteer for a cause I care about</li>
                </ul>
                <ul className="space-y-2 text-sm">
                  <li>• Practice listening more than talking</li>
                  <li>• Take initiative on a project or task</li>
                  <li>• Encourage someone who is struggling</li>
                  <li>• Learn from a leader I admire</li>
                  <li>• Reflect on my leadership strengths and growth areas</li>
                </ul>
              </div>
            </div>

            <p className="text-purple-100 text-sm drop-shadow-sm">
              Remember: Leadership is not about being the boss - it's about being of service. Lead with humility, 
              integrity, and compassion, and you'll make a lasting positive impact! 👑💪
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default LeadershipPage;
