import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Droplets, Shield, Spark<PERSON>, Clock, CheckCircle, Star, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PersonalHygienePage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-cyan-600 via-blue-600 to-indigo-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                🧼 Personal Hygiene & Grooming
              </h1>
              <p className="text-cyan-100 mt-2 text-lg drop-shadow-md">
                Essential hygiene practices and grooming tips for teenage boys
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-cyan-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-cyan-400/30">
              <Sparkles className="w-6 h-6 text-cyan-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">Looking and Feeling Your Best</h2>
              <p className="text-gray-300">Good hygiene builds confidence and shows respect for yourself and others</p>
            </div>
          </div>

          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            Good personal hygiene is about more than just looking clean - it's about feeling confident, staying healthy,
            and showing respect for yourself and the people around you. As your body changes during puberty, developing
            good hygiene habits becomes even more important.
          </p>

          <div className="bg-cyan-500/10 backdrop-blur-sm border-l-4 border-cyan-400 p-4 rounded-r-lg border border-cyan-400/20">
            <p className="text-cyan-200 font-medium drop-shadow-sm">
              💡 Remember: Good hygiene habits take time to develop, but once they become routine, they'll be second nature.
              Start building these habits now, and they'll serve you well throughout your life!
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Daily Hygiene Routine */}
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
          >
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
              <Clock className="w-6 h-6 text-blue-400" />
              Daily Hygiene Routine
            </h3>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold text-white mb-4 text-green-400 drop-shadow-md">🌅 Morning Routine</h4>
                <ul className="space-y-3 text-gray-200">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Shower:</strong> Start your day fresh with a warm shower using soap or body wash</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Brush teeth:</strong> Use fluoride toothpaste and brush for at least 2 minutes</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Apply deodorant:</strong> Use antiperspirant or deodorant on clean, dry underarms</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Face care:</strong> Wash your face with a gentle cleanser to prevent acne</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Hair care:</strong> Wash and style your hair as needed</span>
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-white mb-4 text-blue-400 drop-shadow-md">🌙 Evening Routine</h4>
                <ul className="space-y-3 text-gray-200">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Brush teeth:</strong> Remove food particles and bacteria before bed</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Wash face:</strong> Remove dirt, oil, and sweat from the day</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Clean hands:</strong> Wash thoroughly with soap and warm water</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Change clothes:</strong> Put on clean pajamas or sleepwear</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Shower if needed:</strong> After sports or heavy sweating</span>
                  </li>
                </ul>
              </div>
            </div>
            {/* Sharp Silver Divider */}
            <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Body Care Specifics */}
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
          >
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
              <Droplets className="w-6 h-6 text-cyan-400" />
              Body Care Essentials
            </h3>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
                <div className="w-12 h-12 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-blue-400/30">
                  <Droplets className="w-6 h-6 text-blue-400" />
                </div>
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">Showering</h4>
                <ul className="text-sm text-gray-200 space-y-2">
                  <li>• Shower daily, especially after exercise</li>
                  <li>• Use warm (not hot) water</li>
                  <li>• Use soap or body wash all over</li>
                  <li>• Pay attention to feet, underarms, and groin</li>
                  <li>• Rinse thoroughly to remove all soap</li>
                </ul>
              </div>

              <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
                <div className="w-12 h-12 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-green-400/30">
                  <Shield className="w-6 h-6 text-green-400" />
                </div>
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">Deodorant Use</h4>
                <ul className="text-sm text-gray-200 space-y-2">
                  <li>• Apply to clean, dry underarms</li>
                  <li>• Use antiperspirant for heavy sweating</li>
                  <li>• Reapply after sports or exercise</li>
                  <li>• Choose unscented if you have sensitive skin</li>
                  <li>• Don't overuse - a little goes a long way</li>
                </ul>
              </div>

              <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
                <div className="w-12 h-12 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-purple-400/30">
                  <Sparkles className="w-6 h-6 text-purple-400" />
                </div>
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">Hair Care</h4>
                <ul className="text-sm text-gray-200 space-y-2">
                  <li>• Wash hair 2-3 times per week (or daily if oily)</li>
                  <li>• Use shampoo appropriate for your hair type</li>
                  <li>• Condition if your hair is dry or damaged</li>
                  <li>• Brush or comb gently when wet</li>
                  <li>• Keep hair neat and well-groomed</li>
                </ul>
              </div>
            </div>
            {/* Sharp Silver Divider */}
            <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Oral Hygiene */}
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Star className="w-6 h-6 text-yellow-600" />
              Oral Hygiene & Fresh Breath
            </h3>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold text-gray-800 mb-4">Daily Oral Care</h4>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Brush twice daily:</strong> Morning and before bed with fluoride toothpaste</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Brush for 2 minutes:</strong> Clean all surfaces of your teeth thoroughly</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Floss daily:</strong> Remove food particles between teeth</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Use mouthwash:</strong> Rinse with antibacterial mouthwash if needed</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Replace toothbrush:</strong> Every 3-4 months or when bristles fray</span>
                  </li>
                </ul>
              </div>

              <div className="bg-yellow-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">Fresh Breath Tips</h4>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Brush your tongue to remove bacteria</li>
                  <li>• Stay hydrated by drinking plenty of water</li>
                  <li>• Avoid foods that cause bad breath (garlic, onions)</li>
                  <li>• Chew sugar-free gum after meals</li>
                  <li>• Don't smoke or use tobacco products</li>
                  <li>• Visit the dentist regularly for checkups</li>
                </ul>
              </div>
            </div>
        </motion.div>

        {/* Skin Care & Acne Prevention */}
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Shield className="w-6 h-6 text-green-600" />
              Skin Care & Acne Prevention
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                During puberty, hormonal changes can cause your skin to produce more oil, leading to acne. 
                A good skincare routine can help keep your skin healthy and prevent breakouts.
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-green-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">✅ Do This for Healthy Skin</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Wash your face twice daily with gentle cleanser</li>
                    <li>• Use lukewarm water, not hot or cold</li>
                    <li>• Pat dry with a clean towel, don't rub</li>
                    <li>• Use oil-free moisturizer if your skin is dry</li>
                    <li>• Wear sunscreen when going outside</li>
                    <li>• Change pillowcases regularly</li>
                    <li>• Eat a healthy diet with plenty of water</li>
                  </ul>
                </div>

                <div className="bg-red-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">❌ Avoid These Habits</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Don't pick, squeeze, or pop pimples</li>
                    <li>• Avoid touching your face with dirty hands</li>
                    <li>• Don't use harsh scrubs or over-wash</li>
                    <li>• Avoid oil-based hair products near your face</li>
                    <li>• Don't share towels or washcloths</li>
                    <li>• Limit greasy or sugary foods</li>
                    <li>• Don't sleep with makeup or dirt on your face</li>
                  </ul>
                </div>
              </div>
            </div>
        </motion.div>

        {/* Grooming & Appearance */}
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Sparkles className="w-6 h-6 text-purple-600" />
              Grooming & Personal Appearance
            </h3>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="w-8 h-8 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">Nail Care</h4>
                <p className="text-gray-600 text-sm mb-3">
                  Keep nails clean and trimmed. Use a nail brush to clean under nails and trim them straight across.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">Foot Care</h4>
                <p className="text-gray-600 text-sm mb-3">
                  Wash feet daily, dry between toes, and wear clean socks. Change shoes regularly to prevent odor.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Star className="w-8 h-8 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">Clean Clothes</h4>
                <p className="text-gray-600 text-sm mb-3">
                  Wear clean clothes daily, especially underwear and socks. Wash clothes regularly and iron when needed.
                </p>
              </div>
            </div>
        </motion.div>

        {/* Building Good Habits */}
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-gradient-to-r from-cyan-600/80 to-blue-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-cyan-400/30"
          >
            <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
              <AlertCircle className="w-6 h-6" />
              Building Lifelong Habits
            </h3>

            <div className="space-y-4">
              <p className="leading-relaxed drop-shadow-sm">
                Good hygiene habits are investments in your future. They boost your confidence, improve your health,
                and show others that you respect yourself and them. Start building these habits now!
              </p>

              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h4 className="font-semibold mb-3 drop-shadow-md">Tips for Success:</h4>
                <ul className="space-y-2 text-sm">
                  <li>• Start with one or two habits and build from there</li>
                  <li>• Set reminders on your phone until habits become automatic</li>
                  <li>• Keep hygiene products easily accessible</li>
                  <li>• Ask family members to help remind you at first</li>
                  <li>• Reward yourself for maintaining good habits</li>
                  <li>• Remember that it takes about 21 days to form a new habit</li>
                </ul>
              </div>

              <p className="text-cyan-100 text-sm drop-shadow-sm">
                Remember: Good hygiene is a sign of self-respect and maturity. You're investing in your future success! 🧼💪
              </p>
            </div>
        </motion.div>
      </div>
    </div>
  );
};

export default PersonalHygienePage;
