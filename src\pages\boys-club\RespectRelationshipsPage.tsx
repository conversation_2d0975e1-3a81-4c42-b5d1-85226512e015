import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Heart, Shield, Users, Star, CheckCircle, AlertTriangle, Award } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const RespectRelationshipsPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                🤝 Respect & Healthy Relationships
              </h1>
              <p className="text-blue-100 mt-2 text-lg drop-shadow-md">
                Understanding respect, consent, and how to build healthy relationships with everyone
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
          {/* Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Heart className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Being a Man of Respect</h2>
                <p className="text-gray-600">Building character through how you treat others</p>
              </div>
            </div>
            
            <p className="text-gray-700 leading-relaxed mb-6">
              Real strength as a man comes from how you treat others - with respect, kindness, and dignity. 
              Whether it's with family, friends, or romantic relationships, learning to show genuine respect 
              and build healthy connections will define your character and help you become the kind of man 
              others look up to and trust.
            </p>

            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
              <p className="text-blue-800 font-medium">
                💡 Remember: How you treat others, especially when no one is watching, reveals your true character. 
                Respect isn't just about being polite - it's about recognizing the worth and dignity of every person.
              </p>
            </div>
          </motion.div>

          {/* What is Respect */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Star className="w-6 h-6 text-yellow-600" />
              Understanding True Respect
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                Respect means recognizing that every person has value, dignity, and rights that deserve to be honored. 
                It's about treating others the way you want to be treated, regardless of their gender, background, 
                or relationship to you.
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-green-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 text-green-700">✅ Respectful Behavior</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Listening when others speak</li>
                    <li>• Honoring people's boundaries and decisions</li>
                    <li>• Speaking kindly, even when disagreeing</li>
                    <li>• Treating everyone with basic human dignity</li>
                    <li>• Keeping your word and being reliable</li>
                    <li>• Apologizing when you make mistakes</li>
                    <li>• Standing up for others who are being mistreated</li>
                  </ul>
                </div>

                <div className="bg-red-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 text-red-700">❌ Disrespectful Behavior</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Interrupting or talking over others</li>
                    <li>• Ignoring someone's "no" or boundaries</li>
                    <li>• Name-calling or put-downs</li>
                    <li>• Making assumptions based on stereotypes</li>
                    <li>• Breaking promises or being unreliable</li>
                    <li>• Refusing to admit when you're wrong</li>
                    <li>• Staying silent when others are being hurt</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Respecting Different Relationships */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Users className="w-6 h-6 text-purple-600" />
              Respect in Different Relationships
            </h3>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="border border-gray-200 rounded-xl p-6">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Heart className="w-6 h-6 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-3">Family Respect</h4>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• Honor your parents and elders</li>
                  <li>• Help with household responsibilities</li>
                  <li>• Speak respectfully, even when upset</li>
                  <li>• Show appreciation for what they do</li>
                  <li>• Be honest and trustworthy</li>
                </ul>
              </div>

              <div className="border border-gray-200 rounded-xl p-6">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-3">Friendship Respect</h4>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• Keep your friends' secrets</li>
                  <li>• Be loyal and supportive</li>
                  <li>• Include others and avoid exclusion</li>
                  <li>• Respect different opinions and interests</li>
                  <li>• Stand up for friends when they need you</li>
                </ul>
              </div>

              <div className="border border-gray-200 rounded-xl p-6">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                  <Heart className="w-6 h-6 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-3">Romantic Respect</h4>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• Always respect boundaries and consent</li>
                  <li>• Communicate openly and honestly</li>
                  <li>• Support their goals and dreams</li>
                  <li>• Never pressure or manipulate</li>
                  <li>• Treat them as an equal partner</li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Understanding Consent */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Shield className="w-6 h-6 text-green-600" />
              Understanding Consent & Boundaries
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                Consent is about respecting another person's right to make decisions about their own body and 
                comfort level. As a man of character, understanding and respecting consent is fundamental to 
                all your relationships.
              </p>

              <div className="bg-green-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">What Consent Means</h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• <strong>Clear:</strong> A definite "yes," not silence or uncertainty</li>
                    <li>• <strong>Voluntary:</strong> Given freely without pressure or threats</li>
                    <li>• <strong>Informed:</strong> Understanding what they're agreeing to</li>
                  </ul>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• <strong>Ongoing:</strong> Can be changed or withdrawn at any time</li>
                    <li>• <strong>Specific:</strong> Agreeing to one thing doesn't mean agreeing to everything</li>
                    <li>• <strong>Sober:</strong> Cannot be given under influence of alcohol or drugs</li>
                  </ul>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-blue-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Respecting Boundaries</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Accept "no" immediately without arguing</li>
                    <li>• Don't try to change someone's mind</li>
                    <li>• Respect physical and emotional boundaries</li>
                    <li>• Check in regularly about comfort levels</li>
                    <li>• Never use guilt, pressure, or manipulation</li>
                  </ul>
                </div>

                <div className="bg-purple-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Communication is Key</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Ask before any physical contact</li>
                    <li>• Pay attention to non-verbal cues</li>
                    <li>• Create a safe space for honest communication</li>
                    <li>• Be willing to slow down or stop</li>
                    <li>• Make sure both people are comfortable</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Healthy Relationship Qualities */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Award className="w-6 h-6 text-orange-600" />
              Building Healthy Relationships
            </h3>

            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-4 text-green-700">✅ Healthy Relationship Qualities</h4>
                  <ul className="space-y-3 text-gray-700">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Mutual respect:</strong> Both people value each other's thoughts and feelings</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Trust:</strong> Honesty and reliability in all interactions</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Communication:</strong> Open, honest dialogue about feelings and needs</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Support:</strong> Encouraging each other's goals and dreams</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Independence:</strong> Maintaining individual identities and friendships</span>
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-800 mb-4 text-red-700">🚩 Unhealthy Patterns to Avoid</h4>
                  <ul className="space-y-3 text-gray-700">
                    <li className="flex items-start gap-2">
                      <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Control:</strong> Trying to control what someone does or who they see</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Jealousy:</strong> Excessive possessiveness or suspicion</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Manipulation:</strong> Using guilt, threats, or emotional pressure</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Disrespect:</strong> Put-downs, name-calling, or dismissing feelings</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Isolation:</strong> Trying to separate someone from friends and family</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Being a Man of Character */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Star className="w-6 h-6 text-yellow-600" />
              Being a Man of Character
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                True masculinity isn't about being tough or dominant - it's about being strong enough to be gentle, 
                confident enough to be humble, and secure enough to treat everyone with respect and dignity.
              </p>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="w-8 h-8 text-blue-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Protector</h4>
                  <p className="text-gray-600 text-sm">
                    Use your strength to protect and defend others, especially those who are vulnerable or being mistreated.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Heart className="w-8 h-8 text-green-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Supporter</h4>
                  <p className="text-gray-600 text-sm">
                    Encourage and support others in their goals, dreams, and personal growth without trying to control them.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="w-8 h-8 text-purple-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Leader</h4>
                  <p className="text-gray-600 text-sm">
                    Lead by example, showing others what respect, integrity, and kindness look like in action.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-lg p-8 text-white"
          >
            <h3 className="text-2xl font-bold mb-6 flex items-center gap-3">
              <Users className="w-6 h-6" />
              Your Commitment to Respect
            </h3>

            <div className="space-y-4">
              <p className="leading-relaxed">
                The world needs more men who understand that true strength comes from treating others with respect, 
                kindness, and dignity. Your character is defined by how you treat people, especially when it's 
                difficult or when no one is watching.
              </p>

              <div className="bg-white/20 backdrop-blur-sm rounded-xl p-6">
                <h4 className="font-semibold mb-3">My Commitment to Respect:</h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <ul className="space-y-2 text-sm">
                    <li>• I will treat all people with dignity and respect</li>
                    <li>• I will always honor boundaries and consent</li>
                    <li>• I will use my strength to protect, not harm</li>
                    <li>• I will communicate honestly and kindly</li>
                  </ul>
                  <ul className="space-y-2 text-sm">
                    <li>• I will stand up for those who are being mistreated</li>
                    <li>• I will take responsibility for my actions</li>
                    <li>• I will support others in achieving their goals</li>
                    <li>• I will be the kind of man others can trust and respect</li>
                  </ul>
                </div>
              </div>

              <p className="text-blue-100 text-sm">
                Remember: Real men respect others, honor boundaries, and use their strength to build up, not tear down!
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default RespectRelationshipsPage;
