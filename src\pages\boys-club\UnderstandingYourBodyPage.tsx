import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Users, Zap, Shield, Activity, CheckCircle, Star, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const UnderstandingYourBodyPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 py-6">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-4 py-2 bg-white/20 hover:bg-white/30 text-white font-medium rounded-lg transition-all duration-300 backdrop-blur-sm"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-2xl md:text-3xl font-bold text-white">
                👦 Understanding Your Changing Body
              </h1>
              <p className="text-blue-100 mt-1">
                A comprehensive guide to male puberty, physical changes, and maintaining good health
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Your Journey to Manhood</h2>
                <p className="text-gray-600">Understanding the changes happening in your body</p>
              </div>
            </div>
            
            <p className="text-gray-700 leading-relaxed mb-6">
              Growing up as a young man is an exciting journey filled with physical, emotional, and mental changes. 
              Your body is becoming stronger and more capable, and understanding these changes will help you feel 
              confident and prepared for the challenges and opportunities ahead.
            </p>

            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
              <p className="text-blue-800 font-medium">
                💡 Remember: Every boy develops at his own pace. Some start puberty as early as 9, others as late as 14. 
                Both are completely normal! This guide will help you understand what to expect.
              </p>
            </div>
          </motion.div>

          {/* Puberty Changes Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Zap className="w-6 h-6 text-yellow-600" />
              Physical Changes During Puberty
            </h3>

            <div className="space-y-6">
              <div className="border-l-4 border-blue-400 pl-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-3">Body Growth & Development</h4>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Growth spurts:</strong> You might grow 4-6 inches taller in a year and gain muscle mass</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Voice changes:</strong> Your voice will crack and eventually become deeper</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Facial hair:</strong> You'll start growing hair on your face, starting with a mustache</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Body hair:</strong> Hair will grow on your chest, arms, legs, and underarms</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Muscle development:</strong> Your muscles will become stronger and more defined</span>
                  </li>
                </ul>
              </div>

              <div className="border-l-4 border-green-400 pl-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-3">Other Physical Changes</h4>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Skin changes:</strong> You might experience oily skin and acne due to hormonal changes</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Body odor:</strong> You'll start sweating more and need to use deodorant</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Genital development:</strong> Your reproductive organs will grow and mature</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Nocturnal emissions:</strong> Wet dreams are a normal part of development</span>
                  </li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Emotional Changes */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Star className="w-6 h-6 text-purple-600" />
              Emotional & Mental Changes
            </h3>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-purple-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">What You Might Experience</h4>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Mood swings and emotional ups and downs</li>
                  <li>• Increased interest in romantic relationships</li>
                  <li>• Desire for more independence and privacy</li>
                  <li>• Feeling more self-conscious about your appearance</li>
                  <li>• Increased competitive feelings</li>
                  <li>• Sometimes feeling confused or overwhelmed</li>
                </ul>
              </div>

              <div className="bg-blue-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">Coping Strategies</h4>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Talk to trusted adults about your feelings</li>
                  <li>• Exercise regularly to manage stress and emotions</li>
                  <li>• Practice deep breathing when feeling overwhelmed</li>
                  <li>• Keep a journal to express your thoughts</li>
                  <li>• Maintain good friendships for support</li>
                  <li>• Remember that these feelings are temporary</li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Taking Care of Your Body */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Activity className="w-6 h-6 text-green-600" />
              Taking Care of Your Changing Body
            </h3>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Activity className="w-8 h-8 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">Stay Active</h4>
                <p className="text-gray-600 text-sm">
                  Regular exercise helps build muscle, improves mood, and gives you energy. Try sports, gym, or outdoor activities.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">Eat Well</h4>
                <p className="text-gray-600 text-sm">
                  Your growing body needs proper nutrition. Eat plenty of protein, fruits, vegetables, and whole grains.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">Get Enough Sleep</h4>
                <p className="text-gray-600 text-sm">
                  Growing bodies need 8-10 hours of sleep. Good sleep helps with growth, mood, and concentration.
                </p>
              </div>
            </div>

            <div className="mt-8 bg-green-50 p-6 rounded-xl">
              <h4 className="font-semibold text-gray-800 mb-3">Daily Health Habits</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Shower daily, especially after exercise</li>
                  <li>• Use deodorant to prevent body odor</li>
                  <li>• Brush your teeth twice daily</li>
                  <li>• Wash your face to prevent acne</li>
                </ul>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Drink plenty of water throughout the day</li>
                  <li>• Limit junk food and sugary drinks</li>
                  <li>• Wear clean clothes, especially underwear</li>
                  <li>• Protect your skin from too much sun</li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Building Confidence */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Star className="w-6 h-6 text-yellow-600" />
              Building Confidence During Changes
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                It's normal to feel uncertain about the changes happening to your body. Remember that everyone 
                develops differently, and there's no "right" timeline for puberty. Focus on being healthy and 
                confident in who you are becoming.
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-yellow-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">💪 Confidence Boosters</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Focus on what your body can do, not just how it looks</li>
                    <li>• Set fitness goals and work towards them</li>
                    <li>• Practice good posture and body language</li>
                    <li>• Develop skills and hobbies you're passionate about</li>
                    <li>• Surround yourself with positive, supportive friends</li>
                  </ul>
                </div>

                <div className="bg-blue-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">🧠 Positive Mindset</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• "I am growing stronger every day"</li>
                    <li>• "My body is capable of amazing things"</li>
                    <li>• "I treat my body with respect and care"</li>
                    <li>• "I am becoming the man I want to be"</li>
                    <li>• "I embrace the changes that make me unique"</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* When to Seek Help */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-lg p-8 text-white"
          >
            <h3 className="text-2xl font-bold mb-6 flex items-center gap-3">
              <AlertCircle className="w-6 h-6" />
              When to Talk to Someone
            </h3>

            <div className="space-y-4">
              <p className="leading-relaxed">
                It's important to have trusted adults you can talk to about the changes in your body. Don't be 
                embarrassed to ask questions - every man has gone through these same changes!
              </p>

              <div className="bg-white/20 backdrop-blur-sm rounded-xl p-6">
                <h4 className="font-semibold mb-3">Talk to a trusted adult if:</h4>
                <ul className="space-y-2 text-sm">
                  <li>• You have questions about the changes in your body</li>
                  <li>• You're experiencing pain or discomfort</li>
                  <li>• You're feeling very worried or anxious</li>
                  <li>• You need help with hygiene or health products</li>
                  <li>• You want to learn more about staying healthy and strong</li>
                  <li>• You're being teased or bullied about your development</li>
                </ul>
              </div>

              <p className="text-blue-100 text-sm">
                Remember: Asking questions shows maturity and responsibility. Real men take care of their health!
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default UnderstandingYourBodyPage;
