import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Briefcase, Target, Star, Lightbulb, CheckCircle, Users, Award } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const CareerPlanningPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                💼 Future Career Planning
              </h1>
              <p className="text-green-100 mt-2 text-lg drop-shadow-md">
                Explore career options, understand different paths, and start planning for your professional future
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-green-400/30">
              <Briefcase className="w-6 h-6 text-green-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">Your Future Starts Now</h2>
              <p className="text-gray-300">Planning your career path as a young woman</p>
            </div>
          </div>
          
          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            Your career is more than just a job - it's a way to use your talents, make a difference in the world, 
            and create the life you want. Starting to think about your future career now gives you time to explore 
            options, develop skills, and make informed decisions about your education and goals.
          </p>

          <div className="bg-green-500/10 backdrop-blur-sm border-l-4 border-green-400 p-4 rounded-r-lg border border-green-400/20">
            <p className="text-green-200 font-medium drop-shadow-sm">
              💡 Remember: You don't have to have it all figured out right now! Career planning is about exploring 
              possibilities and keeping your options open while building valuable skills.
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Career Exploration */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Target className="w-6 h-6 text-blue-400" />
            Exploring Career Fields
          </h3>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🔬 STEM Careers</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Engineering (software, biomedical, environmental)</li>
                  <li>• Medicine and healthcare</li>
                  <li>• Research and laboratory sciences</li>
                  <li>• Technology and computer science</li>
                  <li>• Mathematics and data analysis</li>
                </ul>
              </div>

              <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🎨 Creative Fields</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Graphic design and digital arts</li>
                  <li>• Writing and journalism</li>
                  <li>• Film and media production</li>
                  <li>• Fashion and interior design</li>
                  <li>• Music and performing arts</li>
                </ul>
              </div>

              <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🏢 Business & Leadership</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Entrepreneurship and startups</li>
                  <li>• Marketing and communications</li>
                  <li>• Finance and accounting</li>
                  <li>• Human resources and management</li>
                  <li>• International business</li>
                </ul>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-pink-500/10 backdrop-blur-sm rounded-xl p-6 border border-pink-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">❤️ Service & Care</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Education and teaching</li>
                  <li>• Social work and counseling</li>
                  <li>• Nursing and healthcare</li>
                  <li>• Non-profit and advocacy work</li>
                  <li>• Psychology and mental health</li>
                </ul>
              </div>

              <div className="bg-orange-500/10 backdrop-blur-sm rounded-xl p-6 border border-orange-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">⚖️ Law & Government</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Legal practice and law</li>
                  <li>• Politics and public service</li>
                  <li>• International relations</li>
                  <li>• Criminal justice</li>
                  <li>• Policy and advocacy</li>
                </ul>
              </div>

              <div className="bg-cyan-500/10 backdrop-blur-sm rounded-xl p-6 border border-cyan-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🌍 Emerging Fields</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Sustainability and green energy</li>
                  <li>• Artificial intelligence and robotics</li>
                  <li>• Digital marketing and social media</li>
                  <li>• Biotechnology and genetics</li>
                  <li>• Space exploration and aerospace</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Skills Development */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Star className="w-6 h-6 text-yellow-400" />
            Building Career-Ready Skills
          </h3>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-yellow-400/30">
                <Lightbulb className="w-8 h-8 text-yellow-400" />
              </div>
              <h4 className="font-semibold text-white mb-2 drop-shadow-md">Academic Excellence</h4>
              <p className="text-gray-200 text-sm">
                Focus on your studies, develop strong reading and writing skills, and excel in subjects that interest you.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-400/30">
                <Users className="w-8 h-8 text-blue-400" />
              </div>
              <h4 className="font-semibold text-white mb-2 drop-shadow-md">Leadership Skills</h4>
              <p className="text-gray-200 text-sm">
                Take on leadership roles in clubs, sports, or volunteer activities to develop confidence and teamwork skills.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-green-400/30">
                <Award className="w-8 h-8 text-green-400" />
              </div>
              <h4 className="font-semibold text-white mb-2 drop-shadow-md">Real-World Experience</h4>
              <p className="text-gray-200 text-sm">
                Seek internships, job shadowing, and volunteer opportunities to gain practical experience.
              </p>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Education Pathways */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Award className="w-6 h-6 text-purple-400" />
            Education and Training Options
          </h3>

          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🎓 College & University</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Bachelor's degrees in your field of interest</li>
                  <li>• Research universities vs. liberal arts colleges</li>
                  <li>• Consider location, size, and campus culture</li>
                  <li>• Look into scholarship and financial aid options</li>
                  <li>• Explore study abroad opportunities</li>
                </ul>
              </div>

              <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🔧 Technical & Trade Schools</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Specialized training in specific skills</li>
                  <li>• Shorter programs with direct job placement</li>
                  <li>• Often more affordable than traditional college</li>
                  <li>• High demand for skilled trades</li>
                  <li>• Can lead to entrepreneurship opportunities</li>
                </ul>
              </div>
            </div>

            <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">💡 Alternative Pathways</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Online courses and certifications</li>
                  <li>• Apprenticeships and mentorship programs</li>
                  <li>• Military service with career training</li>
                  <li>• Gap year programs and volunteer work</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Starting your own business</li>
                  <li>• Professional development programs</li>
                  <li>• Industry-specific bootcamps</li>
                  <li>• Combining work and education</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Action Plan */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gradient-to-r from-green-600/80 to-blue-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-green-400/30"
        >
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
            <Target className="w-6 h-6" />
            Your Career Planning Action Steps
          </h3>

          <div className="space-y-4">
            <p className="leading-relaxed drop-shadow-sm">
              Take charge of your future by starting your career exploration today. Every step you take now 
              brings you closer to a fulfilling and successful career!
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold mb-3 drop-shadow-md">This Month, I Will:</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-sm">
                  <li>• Research three careers that interest me</li>
                  <li>• Talk to adults working in fields I'm curious about</li>
                  <li>• Explore college and training program options</li>
                  <li>• Identify skills I need to develop</li>
                </ul>
                <ul className="space-y-2 text-sm">
                  <li>• Look for volunteer or internship opportunities</li>
                  <li>• Set academic goals for this school year</li>
                  <li>• Join clubs related to my interests</li>
                  <li>• Start building a portfolio of my work</li>
                </ul>
              </div>
            </div>

            <p className="text-green-100 text-sm drop-shadow-sm">
              Remember: Your career journey is unique to you. Stay curious, work hard, and believe in your ability to achieve your dreams!
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default CareerPlanningPage;
