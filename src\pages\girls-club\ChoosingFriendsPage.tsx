import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Users, Heart, Shield, Star, CheckCircle, AlertTriangle, Lightbulb } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const ChoosingFriendsPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                👭 Choosing Good Friends
              </h1>
              <p className="text-green-100 mt-2 text-lg drop-shadow-md">
                Discover how to identify true friends and build meaningful connections
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-green-400/30">
              <Users className="w-6 h-6 text-green-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">The Power of True Friendship</h2>
              <p className="text-gray-300">Building connections that last a lifetime</p>
            </div>
          </div>

          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            Choosing good friends is one of the most important decisions you'll make as a teenager. True friends
            will support you, encourage you to be your best self, and stand by you through both good times and
            challenges. Learning how to identify genuine friends and avoid toxic relationships will help you
            build a strong support network that enriches your life.
          </p>

          <div className="bg-green-500/10 backdrop-blur-sm border-l-4 border-green-400 p-4 rounded-r-lg border border-green-400/20">
            <p className="text-green-200 font-medium drop-shadow-sm">
              💡 Remember: Quality is more important than quantity when it comes to friendships. It's better to
              have a few true friends than many superficial ones.
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* What Makes a Good Friend */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Star className="w-6 h-6 text-yellow-400" />
            Qualities of a True Friend
          </h3>

          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="font-semibold text-white mb-4 text-green-400 drop-shadow-md">✅ Look for These Qualities</h4>
              <ul className="space-y-3 text-gray-200">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Trustworthy:</strong> Keeps your secrets and is honest with you</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Supportive:</strong> Encourages your dreams and celebrates your successes</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Loyal:</strong> Stands by you during difficult times</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Respectful:</strong> Values your opinions and boundaries</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Fun to be around:</strong> Makes you laugh and enjoy life</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Good influence:</strong> Encourages you to make positive choices</span>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold text-white mb-4 text-red-400 drop-shadow-md">🚩 Warning Signs to Avoid</h4>
              <ul className="space-y-3 text-gray-200">
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Gossips about others:</strong> If they gossip to you, they'll gossip about you</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Peer pressure:</strong> Pressures you to do things you're uncomfortable with</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Jealous or competitive:</strong> Gets upset when good things happen to you</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Drama-focused:</strong> Always involved in conflicts and negativity</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Unreliable:</strong> Frequently cancels plans or breaks promises</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Disrespectful:</strong> Makes fun of you or dismisses your feelings</span>
                </li>
              </ul>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Types of Friendships */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Users className="w-6 h-6 text-blue-400" />
            Different Types of Friendships
          </h3>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-pink-500/10 backdrop-blur-sm rounded-xl p-6 border border-pink-400/20">
              <div className="w-12 h-12 bg-pink-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-pink-400/30">
                <Heart className="w-6 h-6 text-pink-400" />
              </div>
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">Best Friends</h4>
              <p className="text-gray-200 text-sm mb-3">
                Your closest confidants who know you deeply and accept you completely.
              </p>
              <ul className="text-xs text-gray-200 space-y-1">
                <li>• Share your deepest thoughts and feelings</li>
                <li>• Support you through everything</li>
                <li>• Usually 1-3 very close friends</li>
                <li>• Built on deep trust and understanding</li>
              </ul>
            </div>

            <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
              <div className="w-12 h-12 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-blue-400/30">
                <Users className="w-6 h-6 text-blue-400" />
              </div>
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">Close Friends</h4>
              <p className="text-gray-200 text-sm mb-3">
                Good friends you enjoy spending time with and can rely on for support.
              </p>
              <ul className="text-xs text-gray-200 space-y-1">
                <li>• Share common interests and activities</li>
                <li>• Hang out regularly</li>
                <li>• Trust each other with important things</li>
                <li>• Usually your main friend group</li>
              </ul>
            </div>

            <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
              <div className="w-12 h-12 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-green-400/30">
                <Star className="w-6 h-6 text-green-400" />
              </div>
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">Casual Friends</h4>
              <p className="text-gray-200 text-sm mb-3">
                Friendly acquaintances you enjoy being around in certain settings.
              </p>
              <ul className="text-xs text-gray-200 space-y-1">
                <li>• Classmates, teammates, neighbors</li>
                <li>• Fun to be around in groups</li>
                <li>• Share some common interests</li>
                <li>• May develop into closer friendships</li>
              </ul>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Making New Friends */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Lightbulb className="w-6 h-6 text-yellow-400" />
            How to Make New Friends
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              Making new friends can feel scary, but it's a skill you can develop. The key is to be yourself,
              show genuine interest in others, and put yourself in situations where you can meet like-minded people.
            </p>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-yellow-500/10 backdrop-blur-sm p-6 rounded-xl border border-yellow-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🌟 Where to Meet Friends</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Join clubs or activities you're interested in</li>
                  <li>• Participate in sports teams or fitness classes</li>
                  <li>• Volunteer for causes you care about</li>
                  <li>• Attend school events and social activities</li>
                  <li>• Take classes or workshops</li>
                  <li>• Connect with neighbors or family friends</li>
                </ul>
              </div>

              <div className="bg-blue-500/10 backdrop-blur-sm p-6 rounded-xl border border-blue-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">💬 Conversation Starters</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• "I love your [item of clothing/accessory]!"</li>
                  <li>• "What did you think of [shared experience]?"</li>
                  <li>• "Have you been to [local place/event]?"</li>
                  <li>• "I'm new here, any recommendations for...?"</li>
                  <li>• "That's such a cool [hobby/interest]!"</li>
                  <li>• "Would you like to [activity] together sometime?"</li>
                </ul>
              </div>
            </div>

            <div className="bg-green-500/10 backdrop-blur-sm p-6 rounded-xl border border-green-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">✨ Tips for Building Friendships</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Be genuinely interested in others</li>
                  <li>• Listen more than you talk</li>
                  <li>• Be yourself - don't try to be someone you're not</li>
                  <li>• Show kindness and empathy</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Follow through on plans and commitments</li>
                  <li>• Be patient - friendships take time to develop</li>
                  <li>• Include others and be welcoming</li>
                  <li>• Share appropriate personal information gradually</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Dealing with Friend Problems */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Shield className="w-6 h-6 text-purple-400" />
            Handling Friend Challenges
          </h3>

          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-purple-500/10 backdrop-blur-sm p-6 rounded-xl border border-purple-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">When Friends Disappoint You</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Talk to them directly about the issue</li>
                  <li>• Use "I" statements to express your feelings</li>
                  <li>• Listen to their perspective</li>
                  <li>• Be willing to forgive minor mistakes</li>
                  <li>• Set boundaries if needed</li>
                  <li>• Know when to walk away from toxic friendships</li>
                </ul>
              </div>

              <div className="bg-pink-500/10 backdrop-blur-sm p-6 rounded-xl border border-pink-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">Dealing with Friend Drama</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Stay out of gossip and rumors</li>
                  <li>• Don't take sides in conflicts</li>
                  <li>• Encourage friends to talk directly to each other</li>
                  <li>• Focus on your own friendships</li>
                  <li>• Seek help from trusted adults if needed</li>
                  <li>• Remember that some drama will pass with time</li>
                </ul>
              </div>
            </div>

            <div className="bg-yellow-500/10 backdrop-blur-sm border-l-4 border-yellow-400 p-4 rounded-r-lg border border-yellow-400/20">
              <p className="text-yellow-200 text-sm drop-shadow-sm">
                <strong className="text-white">Remember:</strong> It's okay to outgrow friendships or realize that someone isn't a good
                friend for you. Ending unhealthy friendships makes room for better ones to develop.
              </p>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Being a Good Friend */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gradient-to-r from-green-600/80 to-blue-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-green-400/30"
        >
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
            <Heart className="w-6 h-6" />
            Being the Friend You Want to Have
          </h3>

          <div className="space-y-4">
            <p className="leading-relaxed drop-shadow-sm">
              The best way to attract good friends is to be a good friend yourself. Focus on developing the
              qualities you value in others, and you'll naturally attract people who share your values.
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold mb-3 drop-shadow-md">Ways to Be an Amazing Friend:</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-sm">
                  <li>• Be a good listener and show genuine interest</li>
                  <li>• Keep secrets and be trustworthy</li>
                  <li>• Celebrate your friends' successes</li>
                  <li>• Offer support during difficult times</li>
                </ul>
                <ul className="space-y-2 text-sm">
                  <li>• Be reliable and keep your promises</li>
                  <li>• Include others and be welcoming</li>
                  <li>• Apologize when you make mistakes</li>
                  <li>• Encourage your friends to be their best selves</li>
                </ul>
              </div>
            </div>

            <p className="text-green-100 text-sm drop-shadow-sm">
              Remember: True friendship is about mutual respect, support, and genuine care for each other's well-being! 👭💚
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ChoosingFriendsPage;
