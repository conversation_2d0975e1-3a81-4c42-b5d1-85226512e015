import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Lightbulb, Star, Target, Heart, CheckCircle, <PERSON>rk<PERSON>, Award } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const DiscoveringGiftsPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-yellow-500 via-orange-500 to-pink-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                ✨ Discovering Your Gifts & Talents
              </h1>
              <p className="text-yellow-100 mt-2 text-lg drop-shadow-md">
                Explore your unique abilities and potential to build confidence and plan your future
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-yellow-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-yellow-400/30">
              <Lightbulb className="w-6 h-6 text-yellow-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">You Are Uniquely Gifted</h2>
              <p className="text-gray-300">Every person has special talents waiting to be discovered</p>
            </div>
          </div>

          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            You have unique gifts, talents, and abilities that make you special. Some you might already know about,
            while others are waiting to be discovered. Understanding your strengths and interests will help you
            build confidence, make decisions about your future, and find ways to contribute meaningfully to the world.
          </p>

          <div className="bg-yellow-500/10 backdrop-blur-sm border-l-4 border-yellow-400 p-4 rounded-r-lg border border-yellow-400/20">
            <p className="text-yellow-200 font-medium drop-shadow-sm">
              💡 Remember: Your gifts aren't just about what you're naturally good at - they also include things
              you're passionate about and willing to work hard to develop!
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

          {/* Types of Gifts */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Star className="w-6 h-6 text-orange-600" />
              Different Types of Gifts & Talents
            </h3>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="bg-pink-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <Lightbulb className="w-5 h-5 text-pink-600" />
                    Academic & Intellectual
                  </h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Strong in math, science, or language arts</li>
                    <li>• Love of reading and learning new things</li>
                    <li>• Good at problem-solving and critical thinking</li>
                    <li>• Excellent memory or analytical skills</li>
                    <li>• Curiosity about how things work</li>
                  </ul>
                </div>

                <div className="bg-blue-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-blue-600" />
                    Creative & Artistic
                  </h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Drawing, painting, or visual arts</li>
                    <li>• Music, singing, or playing instruments</li>
                    <li>• Writing stories, poetry, or journalism</li>
                    <li>• Dancing or performing arts</li>
                    <li>• Fashion design or crafting</li>
                  </ul>
                </div>

                <div className="bg-green-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <Target className="w-5 h-5 text-green-600" />
                    Physical & Athletic
                  </h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Excellence in sports or physical activities</li>
                    <li>• Good coordination and motor skills</li>
                    <li>• Strength, speed, or endurance</li>
                    <li>• Love of outdoor activities</li>
                    <li>• Natural athletic ability</li>
                  </ul>
                </div>
              </div>

              <div className="space-y-4">
                <div className="bg-purple-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <Heart className="w-5 h-5 text-purple-600" />
                    Social & Interpersonal
                  </h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Great at making friends and connecting with people</li>
                    <li>• Natural leadership abilities</li>
                    <li>• Good listener and communicator</li>
                    <li>• Empathy and understanding of others</li>
                    <li>• Ability to resolve conflicts</li>
                  </ul>
                </div>

                <div className="bg-orange-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <Award className="w-5 h-5 text-orange-600" />
                    Practical & Technical
                  </h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Good with technology and computers</li>
                    <li>• Building, fixing, or working with hands</li>
                    <li>• Organizing and planning events</li>
                    <li>• Cooking or culinary skills</li>
                    <li>• Entrepreneurial thinking</li>
                  </ul>
                </div>

                <div className="bg-indigo-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <Star className="w-5 h-5 text-indigo-600" />
                    Spiritual & Service
                  </h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Desire to help and serve others</li>
                    <li>• Strong moral compass and values</li>
                    <li>• Ability to inspire and encourage</li>
                    <li>• Compassion for those in need</li>
                    <li>• Natural wisdom and insight</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Self-Discovery Activities */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Target className="w-6 h-6 text-green-600" />
              Discovering Your Unique Gifts
            </h3>

            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-green-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">🤔 Self-Reflection Questions</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• What activities make you lose track of time?</li>
                    <li>• What do people often compliment you on?</li>
                    <li>• What subjects or activities come easily to you?</li>
                    <li>• What would you do if you knew you couldn't fail?</li>
                    <li>• What problems do you wish you could solve?</li>
                    <li>• What makes you feel most alive and energized?</li>
                  </ul>
                </div>

                <div className="bg-blue-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">🔍 Exploration Activities</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Try new clubs, sports, or activities</li>
                    <li>• Take online quizzes about strengths and interests</li>
                    <li>• Ask family and friends what they see in you</li>
                    <li>• Keep a journal of what you enjoy doing</li>
                    <li>• Volunteer for different causes</li>
                    <li>• Shadow professionals in fields that interest you</li>
                  </ul>
                </div>
              </div>

              <div className="bg-yellow-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">📝 Create Your Gifts Inventory</h4>
                <div className="grid md:grid-cols-3 gap-4">
                  <div>
                    <h5 className="font-medium text-gray-700 mb-2">Natural Talents</h5>
                    <p className="text-gray-600 text-sm">Things you're naturally good at without much effort</p>
                  </div>
                  <div>
                    <h5 className="font-medium text-gray-700 mb-2">Learned Skills</h5>
                    <p className="text-gray-600 text-sm">Abilities you've developed through practice and learning</p>
                  </div>
                  <div>
                    <h5 className="font-medium text-gray-700 mb-2">Passions</h5>
                    <p className="text-gray-600 text-sm">Things you love doing and are excited to learn more about</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Developing Your Gifts */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Award className="w-6 h-6 text-purple-600" />
              Developing & Nurturing Your Talents
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                Discovering your gifts is just the beginning. The real magic happens when you commit to developing 
                and nurturing these talents through practice, learning, and dedication.
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-4 text-purple-700">🌱 Ways to Develop Your Gifts</h4>
                  <ul className="space-y-3 text-gray-700">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Practice regularly:</strong> Set aside time to work on your talents consistently</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Seek mentorship:</strong> Find people who excel in your areas of interest</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Take classes:</strong> Formal learning can accelerate your development</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Join groups:</strong> Connect with others who share your interests</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Set goals:</strong> Create specific, achievable targets for improvement</span>
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-800 mb-4 text-orange-700">🎯 Overcoming Challenges</h4>
                  <ul className="space-y-3 text-gray-700">
                    <li className="flex items-start gap-2">
                      <Star className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Embrace failure:</strong> Mistakes are part of the learning process</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Star className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Stay patient:</strong> Talent development takes time and persistence</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Star className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Ignore comparisons:</strong> Focus on your own growth and progress</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Star className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Stay curious:</strong> Keep exploring and trying new things</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Star className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                      <span><strong>Celebrate progress:</strong> Acknowledge your improvements along the way</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Using Your Gifts */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Heart className="w-6 h-6 text-pink-600" />
              Using Your Gifts to Make a Difference
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                Your gifts aren't just for your own benefit - they're meant to be shared with the world. When you 
                use your talents to help others and contribute to your community, you'll find deep satisfaction and purpose.
              </p>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Heart className="w-8 h-8 text-pink-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Serve Others</h4>
                  <p className="text-gray-600 text-sm">
                    Use your talents to help your family, friends, school, and community in meaningful ways.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Target className="w-8 h-8 text-blue-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Pursue Excellence</h4>
                  <p className="text-gray-600 text-sm">
                    Strive to be the best you can be in your areas of giftedness, setting an example for others.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Sparkles className="w-8 h-8 text-green-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Inspire Others</h4>
                  <p className="text-gray-600 text-sm">
                    Your dedication and growth can motivate others to discover and develop their own gifts.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

        {/* Action Steps */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gradient-to-r from-yellow-600/80 to-orange-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-yellow-400/30"
        >
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
            <Lightbulb className="w-6 h-6" />
            Your Gift Discovery Action Plan
          </h3>

          <div className="space-y-4">
            <p className="leading-relaxed drop-shadow-sm">
              You have incredible potential waiting to be unlocked. Start your journey of discovery today and
              watch as your unique gifts begin to shine and make a difference in the world!
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold mb-3 drop-shadow-md">This Week, I Will:</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-sm">
                  <li>• Try one new activity or hobby</li>
                  <li>• Ask three people what they think I'm good at</li>
                  <li>• Reflect on what activities energize me</li>
                  <li>• Research careers related to my interests</li>
                </ul>
                <ul className="space-y-2 text-sm">
                  <li>• Start a gifts and talents journal</li>
                  <li>• Look for ways to use my current talents to help others</li>
                  <li>• Set one specific goal for developing a talent</li>
                  <li>• Celebrate the unique gifts I already know I have</li>
                </ul>
              </div>
            </div>

            <p className="text-yellow-100 text-sm drop-shadow-sm">
              Remember: You are fearfully and wonderfully made with unique gifts that the world needs! ✨🌟
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default DiscoveringGiftsPage;
