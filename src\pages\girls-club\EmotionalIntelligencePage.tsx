import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Heart, Brain, Users, Lightbulb, CheckCircle, Star, Target } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const EmotionalIntelligencePage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-teal-600 via-cyan-600 to-blue-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                🧠 Emotional Intelligence & Mental Health
              </h1>
              <p className="text-teal-100 mt-2 text-lg drop-shadow-md">
                Understand and manage your emotions, develop empathy, and maintain good mental health
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-teal-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-teal-400/30">
              <Brain className="w-6 h-6 text-teal-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">Understanding Your Emotional World</h2>
              <p className="text-gray-300">Building emotional intelligence for a healthier, happier life</p>
            </div>
          </div>
          
          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            Emotional intelligence is your ability to understand, manage, and express your emotions in healthy ways. 
            It also involves understanding and responding to others' emotions with empathy and compassion. As a young 
            woman, developing strong emotional intelligence will help you build better relationships, make good decisions, 
            and maintain your mental health throughout life.
          </p>

          <div className="bg-teal-500/10 backdrop-blur-sm border-l-4 border-teal-400 p-4 rounded-r-lg border border-teal-400/20">
            <p className="text-teal-200 font-medium drop-shadow-sm">
              💡 Remember: Emotions are not good or bad - they're information. Learning to understand and manage 
              them is a skill that will serve you throughout your life.
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Components of Emotional Intelligence */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Star className="w-6 h-6 text-yellow-400" />
            The Four Components of Emotional Intelligence
          </h3>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-yellow-500/10 backdrop-blur-sm rounded-xl p-6 border border-yellow-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🔍 Self-Awareness</h4>
              <p className="text-gray-200 text-sm mb-3">Understanding your own emotions and how they affect you</p>
              <ul className="space-y-2 text-gray-200 text-sm">
                <li>• Recognizing your emotions as they happen</li>
                <li>• Understanding what triggers different feelings</li>
                <li>• Knowing your strengths and weaknesses</li>
                <li>• Being aware of your values and motivations</li>
                <li>• Understanding how your emotions affect others</li>
              </ul>
            </div>

            <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🎯 Self-Management</h4>
              <p className="text-gray-200 text-sm mb-3">Managing your emotions in healthy and productive ways</p>
              <ul className="space-y-2 text-gray-200 text-sm">
                <li>• Controlling impulsive feelings and behaviors</li>
                <li>• Managing stress and difficult emotions</li>
                <li>• Adapting to change and challenges</li>
                <li>• Following through on commitments</li>
                <li>• Taking initiative and staying motivated</li>
              </ul>
            </div>

            <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">❤️ Social Awareness</h4>
              <p className="text-gray-200 text-sm mb-3">Understanding others' emotions and social dynamics</p>
              <ul className="space-y-2 text-gray-200 text-sm">
                <li>• Reading others' emotions and body language</li>
                <li>• Understanding social situations and dynamics</li>
                <li>• Showing empathy and compassion</li>
                <li>• Recognizing power dynamics in groups</li>
                <li>• Being aware of organizational culture</li>
              </ul>
            </div>

            <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🤝 Relationship Management</h4>
              <p className="text-gray-200 text-sm mb-3">Using emotional awareness to manage relationships successfully</p>
              <ul className="space-y-2 text-gray-200 text-sm">
                <li>• Communicating clearly and effectively</li>
                <li>• Managing conflict constructively</li>
                <li>• Inspiring and influencing others positively</li>
                <li>• Working well in teams</li>
                <li>• Building and maintaining healthy relationships</li>
              </ul>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Managing Difficult Emotions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Heart className="w-6 h-6 text-red-400" />
            Managing Difficult Emotions
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              Everyone experiences difficult emotions like anger, sadness, anxiety, and frustration. The key is learning 
              healthy ways to process and manage these feelings rather than letting them control you.
            </p>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-red-400/30">
                  <Target className="w-8 h-8 text-red-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Identify</h4>
                <p className="text-gray-200 text-sm">
                  Name the emotion you're feeling. "I am feeling angry because..."
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-yellow-400/30">
                  <Lightbulb className="w-8 h-8 text-yellow-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Understand</h4>
                <p className="text-gray-200 text-sm">
                  Ask yourself why you're feeling this way and what triggered it.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-green-400/30">
                  <CheckCircle className="w-8 h-8 text-green-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Respond</h4>
                <p className="text-gray-200 text-sm">
                  Choose a healthy way to express or manage the emotion.
                </p>
              </div>
            </div>

            <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🛠️ Healthy Coping Strategies</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Deep breathing and mindfulness exercises</li>
                  <li>• Physical exercise or movement</li>
                  <li>• Journaling or creative expression</li>
                  <li>• Talking to trusted friends or family</li>
                  <li>• Taking a break or changing your environment</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Listening to music or engaging in hobbies</li>
                  <li>• Practicing gratitude and positive thinking</li>
                  <li>• Getting enough sleep and eating well</li>
                  <li>• Seeking professional help when needed</li>
                  <li>• Using relaxation techniques</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Mental Health Awareness */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Brain className="w-6 h-6 text-purple-400" />
            Mental Health Awareness
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              Mental health is just as important as physical health. It's normal to have ups and downs, but it's 
              important to recognize when you might need extra support and to know that seeking help is a sign of strength.
            </p>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">✅ Signs of Good Mental Health</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Feeling generally positive about yourself</li>
                  <li>• Enjoying activities and relationships</li>
                  <li>• Coping well with daily stress</li>
                  <li>• Feeling connected to others</li>
                  <li>• Having energy for daily activities</li>
                  <li>• Sleeping and eating well</li>
                </ul>
              </div>

              <div className="bg-red-500/10 backdrop-blur-sm rounded-xl p-6 border border-red-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">⚠️ When to Seek Help</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Persistent sadness or hopelessness</li>
                  <li>• Extreme mood swings or irritability</li>
                  <li>• Withdrawal from friends and activities</li>
                  <li>• Changes in sleep or eating patterns</li>
                  <li>• Difficulty concentrating or making decisions</li>
                  <li>• Thoughts of self-harm</li>
                </ul>
              </div>
            </div>

            <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🆘 Getting Support</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Talk to parents, guardians, or trusted adults</li>
                  <li>• Reach out to school counselors or teachers</li>
                  <li>• Contact your healthcare provider</li>
                  <li>• Call teen mental health helplines</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Connect with peer support groups</li>
                  <li>• Use mental health apps and resources</li>
                  <li>• Consider therapy or counseling</li>
                  <li>• Remember: seeking help shows strength</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Building Empathy */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gradient-to-r from-teal-600/80 to-cyan-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-teal-400/30"
        >
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
            <Users className="w-6 h-6" />
            Building Empathy and Emotional Intelligence
          </h3>

          <div className="space-y-4">
            <p className="leading-relaxed drop-shadow-sm">
              Emotional intelligence is a skill that grows with practice. Start building yours today and watch as 
              your relationships improve, your stress decreases, and your overall well-being flourishes.
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold mb-3 drop-shadow-md">Daily Practices for Emotional Intelligence:</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-sm">
                  <li>• Practice mindfulness and self-reflection</li>
                  <li>• Keep an emotion journal</li>
                  <li>• Listen actively to others</li>
                  <li>• Practice empathy by considering others' perspectives</li>
                  <li>• Express gratitude regularly</li>
                </ul>
                <ul className="space-y-2 text-sm">
                  <li>• Ask for feedback from trusted friends</li>
                  <li>• Practice deep breathing when stressed</li>
                  <li>• Volunteer to help others</li>
                  <li>• Read books about emotions and relationships</li>
                  <li>• Celebrate your emotional growth</li>
                </ul>
              </div>
            </div>

            <p className="text-teal-100 text-sm drop-shadow-sm">
              Remember: Emotional intelligence is a superpower that will help you succeed in all areas of life. 
              Be patient with yourself as you learn and grow! 🧠💙
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default EmotionalIntelligencePage;
