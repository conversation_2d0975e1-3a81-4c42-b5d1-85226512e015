import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Heart, Users, Shield, AlertTriangle, CheckCircle, Star, MessageCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const HealthyRelationshipsPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 via-pink-500 to-blue-600 py-6">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-4 py-2 bg-white/20 hover:bg-white/30 text-white font-medium rounded-lg transition-all duration-300 backdrop-blur-sm"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-2xl md:text-3xl font-bold text-white">
                💕 Building Healthy Relationships
              </h1>
              <p className="text-purple-100 mt-1">
                Learn about friendship, family relationships, and setting healthy boundaries
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                <Heart className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">The Foundation of Healthy Relationships</h2>
                <p className="text-gray-600">Building connections that support and empower you</p>
              </div>
            </div>
            
            <p className="text-gray-700 leading-relaxed mb-6">
              Healthy relationships are the foundation of a happy and fulfilling life. Whether it's with family, 
              friends, or future romantic partners, learning how to build and maintain healthy relationships will 
              serve you throughout your entire life. It's about respect, communication, trust, and supporting each other.
            </p>

            <div className="bg-purple-50 border-l-4 border-purple-400 p-4 rounded-r-lg">
              <p className="text-purple-800 font-medium">
                💡 Remember: Healthy relationships should make you feel good about yourself, supported, and respected. 
                You deserve to be treated with kindness and dignity in all your relationships.
              </p>
            </div>
          </motion.div>

          {/* Types of Relationships */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Users className="w-6 h-6 text-blue-600" />
              Different Types of Relationships
            </h3>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="border border-gray-200 rounded-xl p-6">
                <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mb-4">
                  <Heart className="w-6 h-6 text-pink-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-3">Family Relationships</h4>
                <p className="text-gray-600 text-sm mb-3">
                  Your family is your first support system. These relationships teach you about love, trust, and belonging.
                </p>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>• Built on unconditional love</li>
                  <li>• May have challenges but endure</li>
                  <li>• Provide security and identity</li>
                  <li>• Teach you about commitment</li>
                </ul>
              </div>

              <div className="border border-gray-200 rounded-xl p-6">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-3">Friendships</h4>
                <p className="text-gray-600 text-sm mb-3">
                  Friends are the family you choose. Good friendships provide support, fun, and shared experiences.
                </p>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>• Based on mutual interests</li>
                  <li>• Provide emotional support</li>
                  <li>• Help you grow and learn</li>
                  <li>• Should be reciprocal</li>
                </ul>
              </div>

              <div className="border border-gray-200 rounded-xl p-6">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                  <MessageCircle className="w-6 h-6 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-3">Romantic Relationships</h4>
                <p className="text-gray-600 text-sm mb-3">
                  As you grow older, you may develop romantic feelings. These relationships require extra care and respect.
                </p>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>• Include deeper emotional connection</li>
                  <li>• Require clear communication</li>
                  <li>• Need strong boundaries</li>
                  <li>• Should enhance, not define you</li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Characteristics of Healthy Relationships */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Star className="w-6 h-6 text-yellow-600" />
              What Makes a Relationship Healthy?
            </h3>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold text-gray-800 mb-4 text-green-700">✅ Healthy Relationship Signs</h4>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Respect:</strong> You treat each other with kindness and consideration</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Trust:</strong> You can rely on each other and feel secure</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Communication:</strong> You can talk openly and honestly</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Support:</strong> You encourage each other's goals and dreams</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Independence:</strong> You maintain your own identity and interests</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Fun:</strong> You enjoy spending time together and laugh together</span>
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-800 mb-4 text-red-700">🚩 Warning Signs to Watch For</h4>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Disrespect:</strong> Name-calling, put-downs, or dismissing your feelings</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Control:</strong> Trying to control what you do, wear, or who you see</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Jealousy:</strong> Excessive jealousy or possessiveness</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Pressure:</strong> Pressuring you to do things you're not comfortable with</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Isolation:</strong> Trying to separate you from friends and family</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Manipulation:</strong> Using guilt, threats, or emotional manipulation</span>
                  </li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Setting Boundaries */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Shield className="w-6 h-6 text-green-600" />
              Setting Healthy Boundaries
            </h3>

            <p className="text-gray-700 leading-relaxed mb-6">
              Boundaries are like invisible lines that protect your physical, emotional, and mental well-being. 
              They help you maintain your sense of self while building healthy relationships with others.
            </p>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-green-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">Types of Boundaries</h4>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• <strong>Physical:</strong> Your personal space and body</li>
                  <li>• <strong>Emotional:</strong> Your feelings and emotional energy</li>
                  <li>• <strong>Time:</strong> How you spend your time and energy</li>
                  <li>• <strong>Digital:</strong> Your online presence and privacy</li>
                  <li>• <strong>Sexual:</strong> Your comfort level with physical intimacy</li>
                </ul>
              </div>

              <div className="bg-blue-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">How to Set Boundaries</h4>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Be clear and direct about your limits</li>
                  <li>• Use "I" statements to express your needs</li>
                  <li>• Don't apologize for having boundaries</li>
                  <li>• Be consistent in enforcing them</li>
                  <li>• Respect others' boundaries too</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
              <p className="text-yellow-800 text-sm">
                <strong>Remember:</strong> It's okay to say "No" to things that make you uncomfortable. 
                Your boundaries are valid, and people who care about you will respect them.
              </p>
            </div>
          </motion.div>

          {/* Communication Skills */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <MessageCircle className="w-6 h-6 text-purple-600" />
              Communication Skills for Healthy Relationships
            </h3>

            <div className="space-y-6">
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageCircle className="w-8 h-8 text-purple-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Active Listening</h4>
                  <p className="text-gray-600 text-sm">
                    Really listen to what others are saying without planning your response. Show you care about their thoughts and feelings.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Heart className="w-8 h-8 text-pink-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Express Feelings</h4>
                  <p className="text-gray-600 text-sm">
                    Share your emotions honestly and appropriately. Use "I feel..." statements to express yourself clearly.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-blue-600" />
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-2">Resolve Conflicts</h4>
                  <p className="text-gray-600 text-sm">
                    Address disagreements calmly and respectfully. Focus on finding solutions rather than winning arguments.
                  </p>
                </div>
              </div>

              <div className="bg-purple-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">Healthy Communication Tips</h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Use "I" statements instead of "You" statements</li>
                    <li>• Be honest but kind in your words</li>
                    <li>• Ask questions to understand better</li>
                    <li>• Take breaks if conversations get heated</li>
                  </ul>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Avoid name-calling or insults</li>
                    <li>• Focus on specific behaviors, not character</li>
                    <li>• Show appreciation and gratitude</li>
                    <li>• Be willing to apologize when wrong</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Getting Help */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl shadow-lg p-8 text-white"
          >
            <h3 className="text-2xl font-bold mb-6 flex items-center gap-3">
              <Shield className="w-6 h-6" />
              When to Seek Help
            </h3>

            <div className="space-y-4">
              <p className="leading-relaxed">
                If you're in a relationship that doesn't feel healthy or safe, it's important to reach out for help. 
                You deserve to be treated with respect and kindness in all your relationships.
              </p>

              <div className="bg-white/20 backdrop-blur-sm rounded-xl p-6">
                <h4 className="font-semibold mb-3">Talk to a trusted adult if:</h4>
                <ul className="space-y-2 text-sm">
                  <li>• Someone is hurting you physically or emotionally</li>
                  <li>• You feel scared or unsafe in a relationship</li>
                  <li>• Someone is pressuring you to do things you don't want to do</li>
                  <li>• You're being isolated from friends and family</li>
                  <li>• You need help setting or maintaining boundaries</li>
                  <li>• You have questions about healthy relationships</li>
                </ul>
              </div>

              <p className="text-purple-100 text-sm">
                Remember: Asking for help is a sign of strength, not weakness. You deserve healthy, happy relationships!
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default HealthyRelationshipsPage;
