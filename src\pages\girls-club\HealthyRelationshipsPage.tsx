import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Heart, Users, Shield, AlertTriangle, CheckCircle, Star, MessageCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const HealthyRelationshipsPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 via-pink-500 to-blue-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                💕 Building Healthy Relationships
              </h1>
              <p className="text-purple-100 mt-2 text-lg drop-shadow-md">
                Learn about friendship, family relationships, and setting healthy boundaries
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-purple-400/30">
              <Heart className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">The Foundation of Healthy Relationships</h2>
              <p className="text-gray-300">Building connections that support and empower you</p>
            </div>
          </div>

          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            Healthy relationships are the foundation of a happy and fulfilling life. Whether it's with family,
            friends, or future romantic partners, learning how to build and maintain healthy relationships will
            serve you throughout your entire life. It's about respect, communication, trust, and supporting each other.
          </p>

          <div className="bg-purple-500/10 backdrop-blur-sm border-l-4 border-purple-400 p-4 rounded-r-lg border border-purple-400/20">
            <p className="text-purple-200 font-medium drop-shadow-sm">
              💡 Remember: Healthy relationships should make you feel good about yourself, supported, and respected.
              You deserve to be treated with kindness and dignity in all your relationships.
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Types of Relationships */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Users className="w-6 h-6 text-blue-400" />
            Different Types of Relationships
          </h3>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-pink-500/10 backdrop-blur-sm rounded-xl p-6 border border-pink-400/20">
              <div className="w-12 h-12 bg-pink-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-pink-400/30">
                <Heart className="w-6 h-6 text-pink-400" />
              </div>
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">Family Relationships</h4>
              <p className="text-gray-200 text-sm mb-3">
                Your family is your first support system. These relationships teach you about love, trust, and belonging.
              </p>
              <ul className="text-xs text-gray-200 space-y-1">
                <li>• Built on unconditional love</li>
                <li>• May have challenges but endure</li>
                <li>• Provide security and identity</li>
                <li>• Teach you about commitment</li>
              </ul>
            </div>

            <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
              <div className="w-12 h-12 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-blue-400/30">
                <Users className="w-6 h-6 text-blue-400" />
              </div>
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">Friendships</h4>
              <p className="text-gray-200 text-sm mb-3">
                Friends are the family you choose. Good friendships provide support, fun, and shared experiences.
              </p>
              <ul className="text-xs text-gray-200 space-y-1">
                <li>• Based on mutual interests</li>
                <li>• Provide emotional support</li>
                <li>• Help you grow and learn</li>
                <li>• Should be reciprocal</li>
              </ul>
            </div>

            <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
              <div className="w-12 h-12 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-purple-400/30">
                <MessageCircle className="w-6 h-6 text-purple-400" />
              </div>
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">Romantic Relationships</h4>
              <p className="text-gray-200 text-sm mb-3">
                As you grow older, you may develop romantic feelings. These relationships require extra care and respect.
              </p>
              <ul className="text-xs text-gray-200 space-y-1">
                <li>• Include deeper emotional connection</li>
                <li>• Require clear communication</li>
                <li>• Need strong boundaries</li>
                <li>• Should enhance, not define you</li>
              </ul>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Characteristics of Healthy Relationships */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Star className="w-6 h-6 text-yellow-400" />
            What Makes a Relationship Healthy?
          </h3>

          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="font-semibold text-white mb-4 text-green-400 drop-shadow-md">✅ Healthy Relationship Signs</h4>
              <ul className="space-y-3 text-gray-200">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Respect:</strong> You treat each other with kindness and consideration</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Trust:</strong> You can rely on each other and feel secure</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Communication:</strong> You can talk openly and honestly</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Support:</strong> You encourage each other's goals and dreams</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Independence:</strong> You maintain your own identity and interests</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Fun:</strong> You enjoy spending time together and laugh together</span>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold text-white mb-4 text-red-400 drop-shadow-md">🚩 Warning Signs to Watch For</h4>
              <ul className="space-y-3 text-gray-200">
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Disrespect:</strong> Name-calling, put-downs, or dismissing your feelings</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Control:</strong> Trying to control what you do, wear, or who you see</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Jealousy:</strong> Excessive jealousy or possessiveness</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Pressure:</strong> Pressuring you to do things you're not comfortable with</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Isolation:</strong> Trying to separate you from friends and family</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span><strong className="text-white">Manipulation:</strong> Using guilt, threats, or emotional manipulation</span>
                </li>
              </ul>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Setting Boundaries */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Shield className="w-6 h-6 text-green-400" />
            Setting Healthy Boundaries
          </h3>

          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            Boundaries are like invisible lines that protect your physical, emotional, and mental well-being.
            They help you maintain your sense of self while building healthy relationships with others.
          </p>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-green-500/10 backdrop-blur-sm p-6 rounded-xl border border-green-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">Types of Boundaries</h4>
              <ul className="space-y-2 text-gray-200 text-sm">
                <li>• <strong className="text-white">Physical:</strong> Your personal space and body</li>
                <li>• <strong className="text-white">Emotional:</strong> Your feelings and emotional energy</li>
                <li>• <strong className="text-white">Time:</strong> How you spend your time and energy</li>
                <li>• <strong className="text-white">Digital:</strong> Your online presence and privacy</li>
                <li>• <strong className="text-white">Sexual:</strong> Your comfort level with physical intimacy</li>
              </ul>
            </div>

            <div className="bg-blue-500/10 backdrop-blur-sm p-6 rounded-xl border border-blue-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">How to Set Boundaries</h4>
              <ul className="space-y-2 text-gray-200 text-sm">
                <li>• Be clear and direct about your limits</li>
                <li>• Use "I" statements to express your needs</li>
                <li>• Don't apologize for having boundaries</li>
                <li>• Be consistent in enforcing them</li>
                <li>• Respect others' boundaries too</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 bg-yellow-500/10 backdrop-blur-sm border-l-4 border-yellow-400 p-4 rounded-r-lg border border-yellow-400/20">
            <p className="text-yellow-200 text-sm drop-shadow-sm">
              <strong className="text-white">Remember:</strong> It's okay to say "No" to things that make you uncomfortable.
              Your boundaries are valid, and people who care about you will respect them.
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Communication Skills */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <MessageCircle className="w-6 h-6 text-purple-400" />
            Communication Skills for Healthy Relationships
          </h3>

          <div className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-purple-400/30">
                  <MessageCircle className="w-8 h-8 text-purple-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Active Listening</h4>
                <p className="text-gray-200 text-sm">
                  Really listen to what others are saying without planning your response. Show you care about their thoughts and feelings.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-pink-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-pink-400/30">
                  <Heart className="w-8 h-8 text-pink-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Express Feelings</h4>
                <p className="text-gray-200 text-sm">
                  Share your emotions honestly and appropriately. Use "I feel..." statements to express yourself clearly.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-400/30">
                  <CheckCircle className="w-8 h-8 text-blue-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Resolve Conflicts</h4>
                <p className="text-gray-200 text-sm">
                  Address disagreements calmly and respectfully. Focus on finding solutions rather than winning arguments.
                </p>
              </div>
            </div>

            <div className="bg-purple-500/10 backdrop-blur-sm p-6 rounded-xl border border-purple-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">Healthy Communication Tips</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Use "I" statements instead of "You" statements</li>
                  <li>• Be honest but kind in your words</li>
                  <li>• Ask questions to understand better</li>
                  <li>• Take breaks if conversations get heated</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Avoid name-calling or insults</li>
                  <li>• Focus on specific behaviors, not character</li>
                  <li>• Show appreciation and gratitude</li>
                  <li>• Be willing to apologize when wrong</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Getting Help */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gradient-to-r from-purple-600/80 to-pink-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-purple-400/30"
        >
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
            <Shield className="w-6 h-6" />
            When to Seek Help
          </h3>

          <div className="space-y-4">
            <p className="leading-relaxed drop-shadow-sm">
              If you're in a relationship that doesn't feel healthy or safe, it's important to reach out for help.
              You deserve to be treated with respect and kindness in all your relationships.
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold mb-3 drop-shadow-md">Talk to a trusted adult if:</h4>
              <ul className="space-y-2 text-sm">
                <li>• Someone is hurting you physically or emotionally</li>
                <li>• You feel scared or unsafe in a relationship</li>
                <li>• Someone is pressuring you to do things you don't want to do</li>
                <li>• You're being isolated from friends and family</li>
                <li>• You need help setting or maintaining boundaries</li>
                <li>• You have questions about healthy relationships</li>
              </ul>
            </div>

            <p className="text-purple-100 text-sm drop-shadow-sm">
              Remember: Asking for help is a sign of strength, not weakness. You deserve healthy, happy relationships! 💕
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default HealthyRelationshipsPage;
