import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Heart, Calendar, Shield, Droplets, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const MenstrualHealthPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-500 via-pink-500 to-purple-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                🌸 Menstrual Health & Hygiene
              </h1>
              <p className="text-pink-100 mt-2 text-lg drop-shadow-md">
                Everything you need to know about menstruation and managing your period confidently
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
          {/* Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center">
                <Heart className="w-6 h-6 text-pink-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Understanding Your Period</h2>
                <p className="text-gray-600">A natural and normal part of growing up</p>
              </div>
            </div>
            
            <p className="text-gray-700 leading-relaxed mb-6">
              Getting your period is a completely normal part of becoming a woman. While it might seem scary or 
              confusing at first, understanding what happens during menstruation and how to take care of yourself 
              will help you feel confident and prepared.
            </p>

            <div className="bg-pink-50 border-l-4 border-pink-400 p-4 rounded-r-lg">
              <p className="text-pink-800 font-medium">
                💡 Remember: Every girl's experience with periods is different. Some start as early as 9 years old, 
                others as late as 16. Both are completely normal!
              </p>
            </div>
          </motion.div>

          {/* What is Menstruation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Droplets className="w-6 h-6 text-blue-600" />
              What is Menstruation?
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                Menstruation (also called a period) happens when the lining of your uterus (called the endometrium) 
                sheds because no pregnancy has occurred. This results in bleeding that flows out of your body through 
                your vagina for about 3-7 days.
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-blue-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <Calendar className="w-5 h-5 text-blue-600" />
                    The Menstrual Cycle
                  </h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• <strong>Day 1-5:</strong> Menstrual phase (your period)</li>
                    <li>• <strong>Day 1-13:</strong> Follicular phase (egg develops)</li>
                    <li>• <strong>Day 14:</strong> Ovulation (egg is released)</li>
                    <li>• <strong>Day 15-28:</strong> Luteal phase (uterus prepares for pregnancy)</li>
                    <li>• Average cycle: 21-35 days (28 days is common)</li>
                  </ul>
                </div>

                <div className="bg-pink-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <Info className="w-5 h-5 text-pink-600" />
                    What to Expect
                  </h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Flow can be light, medium, or heavy</li>
                    <li>• Color can range from bright red to dark brown</li>
                    <li>• You might have cramps or discomfort</li>
                    <li>• Mood changes are normal</li>
                    <li>• First periods are often irregular</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Period Products */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Shield className="w-6 h-6 text-green-600" />
              Period Products & How to Use Them
            </h3>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="border border-gray-200 rounded-xl p-6">
                <h4 className="font-semibold text-gray-800 mb-3">🩸 Pads</h4>
                <p className="text-gray-600 text-sm mb-3">
                  Stick to the inside of your underwear to absorb menstrual flow.
                </p>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>✓ Easy to use for beginners</li>
                  <li>✓ Available in different sizes</li>
                  <li>✓ Change every 3-4 hours</li>
                  <li>✓ Good for overnight use</li>
                </ul>
              </div>

              <div className="border border-gray-200 rounded-xl p-6">
                <h4 className="font-semibold text-gray-800 mb-3">🩸 Tampons</h4>
                <p className="text-gray-600 text-sm mb-3">
                  Insert into the vagina to absorb flow from inside.
                </p>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>✓ Great for swimming/sports</li>
                  <li>✓ Less bulky than pads</li>
                  <li>✓ Change every 4-6 hours</li>
                  <li>✓ Start with smallest size</li>
                </ul>
              </div>

              <div className="border border-gray-200 rounded-xl p-6">
                <h4 className="font-semibold text-gray-800 mb-3">🩸 Menstrual Cups</h4>
                <p className="text-gray-600 text-sm mb-3">
                  Reusable silicone cup that collects menstrual flow.
                </p>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>✓ Eco-friendly option</li>
                  <li>✓ Can wear for up to 12 hours</li>
                  <li>✓ Cost-effective long-term</li>
                  <li>✓ May take practice to use</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
              <p className="text-yellow-800 text-sm">
                <strong>Important:</strong> Never use tampons or cups for more than the recommended time to avoid 
                Toxic Shock Syndrome (TSS). Always wash your hands before and after handling period products.
              </p>
            </div>
          </motion.div>

          {/* Hygiene Tips */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <CheckCircle className="w-6 h-6 text-green-600" />
              Hygiene & Self-Care Tips
            </h3>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold text-gray-800 mb-4">Daily Hygiene During Your Period</h4>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Shower daily and wash your genital area with mild soap and water</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Change your period product regularly (every 3-6 hours)</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Wear clean, breathable cotton underwear</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Wash your hands before and after changing products</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Dispose of used products properly (wrap in toilet paper)</span>
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-800 mb-4">Managing Discomfort</h4>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start gap-2">
                    <Heart className="w-5 h-5 text-pink-500 mt-0.5 flex-shrink-0" />
                    <span>Use a heating pad or hot water bottle for cramps</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Heart className="w-5 h-5 text-pink-500 mt-0.5 flex-shrink-0" />
                    <span>Take warm baths to relax muscles</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Heart className="w-5 h-5 text-pink-500 mt-0.5 flex-shrink-0" />
                    <span>Do gentle exercise like walking or yoga</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Heart className="w-5 h-5 text-pink-500 mt-0.5 flex-shrink-0" />
                    <span>Drink plenty of water and eat nutritious foods</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Heart className="w-5 h-5 text-pink-500 mt-0.5 flex-shrink-0" />
                    <span>Get enough sleep and rest when needed</span>
                  </li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Emergency Kit */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Shield className="w-6 h-6 text-purple-600" />
              Period Emergency Kit
            </h3>

            <p className="text-gray-700 mb-6">
              Being prepared helps you feel confident and ready for your period, whether you're at home, school, or out with friends.
            </p>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-purple-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">🎒 What to Pack</h4>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• 2-3 pads or tampons</li>
                  <li>• Extra pair of underwear</li>
                  <li>• Small pack of wet wipes</li>
                  <li>• Pain relief medication (if approved by parents)</li>
                  <li>• Small plastic bags for disposal</li>
                  <li>• Dark-colored spare pants/skirt</li>
                </ul>
              </div>

              <div className="bg-pink-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">📱 Period Tracking</h4>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Mark the first day of your period on a calendar</li>
                  <li>• Track how many days your period lasts</li>
                  <li>• Note any symptoms or changes</li>
                  <li>• Use a period tracking app (with parent permission)</li>
                  <li>• Share information with a trusted adult</li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* When to Seek Help */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl shadow-lg p-8 text-white"
          >
            <h3 className="text-2xl font-bold mb-6 flex items-center gap-3">
              <AlertCircle className="w-6 h-6" />
              When to Talk to a Doctor or Trusted Adult
            </h3>

            <div className="space-y-4">
              <p className="leading-relaxed">
                While periods are normal, there are times when you should seek help from a healthcare provider or trusted adult.
              </p>

              <div className="bg-white/20 backdrop-blur-sm rounded-xl p-6">
                <h4 className="font-semibold mb-3">Seek help if you experience:</h4>
                <ul className="space-y-2 text-sm">
                  <li>• Severe pain that interferes with daily activities</li>
                  <li>• Very heavy bleeding (changing products every hour)</li>
                  <li>• Periods lasting longer than 7 days</li>
                  <li>• No period by age 16</li>
                  <li>• Sudden changes in your normal cycle</li>
                  <li>• Signs of infection (unusual odor, itching, fever)</li>
                </ul>
              </div>

              <p className="text-pink-100 text-sm">
                Remember: Your health is important, and there's no shame in asking questions or seeking help!
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default MenstrualHealthPage;
