import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Shield, AlertTriangle, Phone, Eye, CheckCircle, Users, Lock } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PersonalSafetyPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-600 via-orange-600 to-yellow-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                🛡️ Personal Safety & Protection
              </h1>
              <p className="text-red-100 mt-2 text-lg drop-shadow-md">
                Learn about personal safety, recognizing dangerous situations, and protecting yourself confidently
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-red-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-red-400/30">
              <Shield className="w-6 h-6 text-red-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">Your Safety Matters</h2>
              <p className="text-gray-300">Empowering yourself with knowledge and confidence</p>
            </div>
          </div>
          
          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            Personal safety is about being aware, prepared, and confident in your ability to protect yourself. 
            As a young woman, learning about safety doesn't mean living in fear - it means being empowered with 
            knowledge and skills that help you navigate the world confidently and make smart decisions.
          </p>

          <div className="bg-red-500/10 backdrop-blur-sm border-l-4 border-red-400 p-4 rounded-r-lg border border-red-400/20">
            <p className="text-red-200 font-medium drop-shadow-sm">
              💡 Remember: Being safety-conscious is a sign of wisdom, not weakness. Trust your instincts - 
              they are often your best protection.
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Situational Awareness */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Eye className="w-6 h-6 text-blue-400" />
            Situational Awareness
          </h3>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">👀 Stay Alert</h4>
              <ul className="space-y-2 text-gray-200 text-sm">
                <li>• Keep your head up and eyes scanning your environment</li>
                <li>• Limit distractions like phones and headphones in public</li>
                <li>• Notice who's around you and any unusual behavior</li>
                <li>• Trust your gut feelings about people and situations</li>
                <li>• Be aware of exits and escape routes</li>
              </ul>
            </div>

            <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🚶‍♀️ Safe Movement</h4>
              <ul className="space-y-2 text-gray-200 text-sm">
                <li>• Walk confidently with purpose and good posture</li>
                <li>• Stick to well-lit, populated areas when possible</li>
                <li>• Avoid shortcuts through isolated areas</li>
                <li>• Let someone know where you're going and when to expect you</li>
                <li>• Have your keys ready before approaching your car or home</li>
              </ul>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Digital Safety */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Lock className="w-6 h-6 text-purple-400" />
            Digital & Online Safety
          </h3>

          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🔒 Privacy Protection</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Keep personal information private on social media</li>
                  <li>• Don't share your location in real-time</li>
                  <li>• Use strong, unique passwords for all accounts</li>
                  <li>• Be careful about what photos you share</li>
                  <li>• Review privacy settings regularly</li>
                </ul>
              </div>

              <div className="bg-orange-500/10 backdrop-blur-sm rounded-xl p-6 border border-orange-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">⚠️ Online Red Flags</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Someone asking for personal information too quickly</li>
                  <li>• Requests for photos or video calls from strangers</li>
                  <li>• People who want to meet in person immediately</li>
                  <li>• Anyone asking you to keep conversations secret</li>
                  <li>• Offers that seem too good to be true</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Emergency Preparedness */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Phone className="w-6 h-6 text-green-400" />
            Emergency Preparedness
          </h3>

          <div className="space-y-6">
            <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">📱 Emergency Contacts</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Program emergency numbers in your phone</li>
                  <li>• Include local police, fire, and medical services</li>
                  <li>• Add trusted family members and friends</li>
                  <li>• Know how to quickly access emergency contacts</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Consider a safety app with location sharing</li>
                  <li>• Learn your local emergency numbers</li>
                  <li>• Keep important numbers written down too</li>
                  <li>• Practice making emergency calls</li>
                </ul>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🎒 Safety Kit</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Fully charged phone with emergency contacts</li>
                  <li>• Small flashlight or phone flashlight</li>
                  <li>• Whistle for attracting attention</li>
                  <li>• Cash for emergencies</li>
                  <li>• Medical information and emergency contacts card</li>
                </ul>
              </div>

              <div className="bg-yellow-500/10 backdrop-blur-sm rounded-xl p-6 border border-yellow-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md">🗣️ Communication Plan</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Always tell someone where you're going</li>
                  <li>• Set check-in times with family or friends</li>
                  <li>• Have a code word for emergencies</li>
                  <li>• Know how to contact help if your phone dies</li>
                  <li>• Practice your safety plan with family</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Self-Defense Basics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Shield className="w-6 h-6 text-red-400" />
            Basic Self-Defense Principles
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              Self-defense is about awareness, avoidance, and escape - not fighting. The goal is always to get 
              away safely, not to engage in confrontation.
            </p>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-red-400/30">
                  <Eye className="w-8 h-8 text-red-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Awareness</h4>
                <p className="text-gray-200 text-sm">
                  Stay alert to your surroundings and trust your instincts about potentially dangerous situations.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-yellow-400/30">
                  <AlertTriangle className="w-8 h-8 text-yellow-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Avoidance</h4>
                <p className="text-gray-200 text-sm">
                  Remove yourself from potentially dangerous situations before they escalate.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-green-400/30">
                  <CheckCircle className="w-8 h-8 text-green-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Escape</h4>
                <p className="text-gray-200 text-sm">
                  If confronted, your priority is to get away safely and seek help from others.
                </p>
              </div>
            </div>

            <div className="bg-red-500/10 backdrop-blur-sm rounded-xl p-6 border border-red-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🥋 Consider Self-Defense Training</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Look for women's self-defense classes in your area</li>
                  <li>• Learn basic techniques from qualified instructors</li>
                  <li>• Practice situational awareness exercises</li>
                  <li>• Build confidence through training</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Focus on escape and evasion techniques</li>
                  <li>• Learn to use your voice as a weapon</li>
                  <li>• Practice with friends or family</li>
                  <li>• Remember: the best fight is the one you avoid</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Empowerment Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gradient-to-r from-red-600/80 to-orange-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-red-400/30"
        >
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
            <Users className="w-6 h-6" />
            You Are Strong and Capable
          </h3>

          <div className="space-y-4">
            <p className="leading-relaxed drop-shadow-sm">
              Personal safety is about empowerment, not fear. By being aware, prepared, and confident, 
              you can navigate the world safely while living your life to the fullest.
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold mb-3 drop-shadow-md">My Safety Commitment:</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-sm">
                  <li>• I will trust my instincts and intuition</li>
                  <li>• I will stay aware of my surroundings</li>
                  <li>• I will communicate my plans with trusted people</li>
                  <li>• I will avoid unnecessary risks</li>
                </ul>
                <ul className="space-y-2 text-sm">
                  <li>• I will seek help when I need it</li>
                  <li>• I will continue learning about safety</li>
                  <li>• I will help other women stay safe too</li>
                  <li>• I will live confidently and fearlessly</li>
                </ul>
              </div>
            </div>

            <p className="text-red-100 text-sm drop-shadow-sm">
              Remember: You have the right to feel safe and protected. Never hesitate to remove yourself from 
              situations that don't feel right!
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default PersonalSafetyPage;
