import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Star, Heart, Lightbulb, CheckCircle, Award, Users, Target } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const SelfConfidencePage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-600 via-pink-600 to-purple-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                ⭐ Self-Confidence & Self-Esteem
              </h1>
              <p className="text-orange-100 mt-2 text-lg drop-shadow-md">
                Build unshakeable confidence, develop positive self-image, and learn to value yourself
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-orange-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-orange-400/30">
              <Star className="w-6 h-6 text-orange-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">You Are Amazing</h2>
              <p className="text-gray-300">Building confidence from the inside out</p>
            </div>
          </div>
          
          <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
            Self-confidence and self-esteem are the foundation of a happy, successful life. They affect how you 
            see yourself, how you interact with others, and what you believe you can achieve. Building strong 
            self-confidence isn't about being perfect - it's about accepting yourself, recognizing your worth, 
            and believing in your ability to grow and succeed.
          </p>

          <div className="bg-orange-500/10 backdrop-blur-sm border-l-4 border-orange-400 p-4 rounded-r-lg border border-orange-400/20">
            <p className="text-orange-200 font-medium drop-shadow-sm">
              💡 Remember: Confidence isn't about thinking you're better than others - it's about not having 
              to compare yourself to others at all. You are uniquely valuable just as you are!
            </p>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Understanding Self-Esteem */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Heart className="w-6 h-6 text-pink-400" />
            Understanding Self-Esteem
          </h3>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-pink-500/10 backdrop-blur-sm rounded-xl p-6 border border-pink-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">💖 Healthy Self-Esteem</h4>
              <ul className="space-y-2 text-gray-200 text-sm">
                <li>• Accepting yourself with both strengths and flaws</li>
                <li>• Believing you deserve love and respect</li>
                <li>• Taking care of your physical and emotional needs</li>
                <li>• Setting boundaries and saying no when necessary</li>
                <li>• Celebrating your achievements, big and small</li>
                <li>• Learning from mistakes without harsh self-criticism</li>
              </ul>
            </div>

            <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🚩 Signs of Low Self-Esteem</h4>
              <ul className="space-y-2 text-gray-200 text-sm">
                <li>• Constantly comparing yourself to others</li>
                <li>• Difficulty accepting compliments</li>
                <li>• Fear of trying new things due to failure</li>
                <li>• Seeking constant approval from others</li>
                <li>• Negative self-talk and self-criticism</li>
                <li>• Avoiding challenges or opportunities</li>
              </ul>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Building Confidence */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Lightbulb className="w-6 h-6 text-yellow-400" />
            Building Unshakeable Confidence
          </h3>

          <div className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-yellow-400/30">
                  <Target className="w-8 h-8 text-yellow-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Set Achievable Goals</h4>
                <p className="text-gray-200 text-sm">
                  Start with small, realistic goals and celebrate when you achieve them. Success builds confidence.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-green-400/30">
                  <Award className="w-8 h-8 text-green-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Develop Your Skills</h4>
                <p className="text-gray-200 text-sm">
                  Focus on improving in areas you care about. Competence naturally leads to confidence.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-purple-400/30">
                  <Users className="w-8 h-8 text-purple-400" />
                </div>
                <h4 className="font-semibold text-white mb-2 drop-shadow-md">Surround Yourself with Support</h4>
                <p className="text-gray-200 text-sm">
                  Spend time with people who believe in you and encourage your growth and dreams.
                </p>
              </div>
            </div>

            <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🌟 Daily Confidence Builders</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Practice positive self-talk and affirmations</li>
                  <li>• Keep a journal of your accomplishments</li>
                  <li>• Try something new or challenging regularly</li>
                  <li>• Help others and volunteer in your community</li>
                  <li>• Take care of your physical health and appearance</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Learn from failures instead of dwelling on them</li>
                  <li>• Practice good posture and confident body language</li>
                  <li>• Speak up for yourself and express your opinions</li>
                  <li>• Celebrate your unique qualities and differences</li>
                  <li>• Focus on progress, not perfection</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Positive Self-Talk */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Heart className="w-6 h-6 text-pink-400" />
            Transforming Your Inner Voice
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              The way you talk to yourself matters more than you might think. Your inner voice can be your biggest 
              supporter or your harshest critic. Learning to speak to yourself with kindness and encouragement 
              is one of the most powerful ways to build lasting confidence.
            </p>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-red-500/10 backdrop-blur-sm rounded-xl p-6 border border-red-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md text-red-400">❌ Negative Self-Talk</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• "I'm not good enough"</li>
                  <li>• "I always mess things up"</li>
                  <li>• "Everyone is better than me"</li>
                  <li>• "I can't do anything right"</li>
                  <li>• "I'm so stupid"</li>
                  <li>• "Nobody likes me"</li>
                </ul>
              </div>

              <div className="bg-green-500/10 backdrop-blur-sm rounded-xl p-6 border border-green-400/20">
                <h4 className="font-semibold text-white mb-3 drop-shadow-md text-green-400">✅ Positive Self-Talk</h4>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• "I am learning and growing every day"</li>
                  <li>• "I can learn from my mistakes"</li>
                  <li>• "I have unique strengths and talents"</li>
                  <li>• "I am capable of improving"</li>
                  <li>• "I am intelligent and capable"</li>
                  <li>• "I am worthy of love and friendship"</li>
                </ul>
              </div>
            </div>

            <div className="bg-purple-500/10 backdrop-blur-sm rounded-xl p-6 border border-purple-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">💜 Daily Affirmations for Girls</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• "I am strong, capable, and resilient"</li>
                  <li>• "My voice and opinions matter"</li>
                  <li>• "I deserve respect and kindness"</li>
                  <li>• "I am beautiful inside and out"</li>
                  <li>• "I can achieve my dreams through hard work"</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• "I am worthy of love and belonging"</li>
                  <li>• "I choose to focus on my strengths"</li>
                  <li>• "I am becoming the woman I want to be"</li>
                  <li>• "I trust myself to make good decisions"</li>
                  <li>• "I celebrate what makes me unique"</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Dealing with Setbacks */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
        >
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
            <Award className="w-6 h-6 text-blue-400" />
            Bouncing Back from Setbacks
          </h3>

          <div className="space-y-6">
            <p className="text-gray-200 leading-relaxed drop-shadow-sm">
              Everyone faces challenges, failures, and disappointments. What matters isn't avoiding these experiences, 
              but learning how to bounce back from them stronger and more confident than before.
            </p>

            <div className="bg-blue-500/10 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20">
              <h4 className="font-semibold text-white mb-3 drop-shadow-md">🔄 Resilience Strategies</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• View failures as learning opportunities</li>
                  <li>• Focus on what you can control</li>
                  <li>• Seek support from trusted friends and family</li>
                  <li>• Practice self-compassion during difficult times</li>
                  <li>• Remember that setbacks are temporary</li>
                </ul>
                <ul className="space-y-2 text-gray-200 text-sm">
                  <li>• Look for the lessons in every experience</li>
                  <li>• Celebrate small steps forward</li>
                  <li>• Maintain perspective on the bigger picture</li>
                  <li>• Use challenges as opportunities to grow stronger</li>
                  <li>• Keep moving forward, even if progress is slow</li>
                </ul>
              </div>
            </div>
          </div>
          {/* Sharp Silver Divider */}
          <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </motion.div>

        {/* Confidence Action Plan */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gradient-to-r from-orange-600/80 to-pink-600/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 text-white border border-orange-400/30"
        >
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-3 drop-shadow-lg">
            <Star className="w-6 h-6" />
            Your Confidence Building Action Plan
          </h3>

          <div className="space-y-4">
            <p className="leading-relaxed drop-shadow-sm">
              Building confidence is a journey, not a destination. Start today with small steps, and watch as 
              your self-esteem grows stronger every day. You have everything within you to become the confident, 
              amazing woman you're meant to be!
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold mb-3 drop-shadow-md">This Week, I Will:</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-sm">
                  <li>• Write down three things I like about myself</li>
                  <li>• Practice positive self-talk daily</li>
                  <li>• Set one small, achievable goal</li>
                  <li>• Try something new that challenges me</li>
                  <li>• Compliment myself when I do well</li>
                </ul>
                <ul className="space-y-2 text-sm">
                  <li>• Speak up in at least one conversation</li>
                  <li>• Help someone else feel confident</li>
                  <li>• Practice good posture and confident body language</li>
                  <li>• Celebrate a recent accomplishment</li>
                  <li>• Remind myself that I am worthy of love and respect</li>
                </ul>
              </div>
            </div>

            <p className="text-orange-100 text-sm drop-shadow-sm">
              Remember: You are amazing, capable, and worthy of all the good things life has to offer. 
              Believe in yourself - you've got this! ⭐
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default SelfConfidencePage;
