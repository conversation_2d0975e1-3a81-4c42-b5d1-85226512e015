import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Heart, Shield, Users, AlertCircle, CheckCircle, Lock, Info } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const SexEducationPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-rose-500 via-pink-500 to-purple-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                💕 Understanding Sexuality & Relationships
              </h1>
              <p className="text-rose-100 mt-2 text-lg drop-shadow-md">
                Comprehensive, age-appropriate education about sexuality, relationships, and personal safety
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
          {/* Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-rose-100 rounded-full flex items-center justify-center">
                <Heart className="w-6 h-6 text-rose-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Understanding Your Sexuality</h2>
                <p className="text-gray-600">Knowledge, respect, and healthy choices</p>
              </div>
            </div>
            
            <p className="text-gray-700 leading-relaxed mb-6">
              As you grow into a young woman, it's natural to have questions about sexuality, relationships, and 
              intimacy. Having accurate, age-appropriate information helps you make informed decisions, stay safe, 
              and develop healthy attitudes about your body and relationships. This is about understanding yourself, 
              respecting others, and making choices that align with your values.
            </p>

            <div className="bg-rose-50 border-l-4 border-rose-400 p-4 rounded-r-lg">
              <p className="text-rose-800 font-medium">
                💡 Remember: Sexuality is a normal part of human development. Having questions is natural, and 
                seeking accurate information shows maturity and responsibility.
              </p>
            </div>
          </motion.div>

          {/* Understanding Your Body */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Info className="w-6 h-6 text-blue-600" />
              Understanding Your Sexual Development
            </h3>

            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-blue-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Physical Development</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Sexual feelings and attractions are normal during puberty</li>
                    <li>• Your body produces hormones that create new sensations</li>
                    <li>• Curiosity about sexuality is a natural part of growing up</li>
                    <li>• Everyone develops at their own pace</li>
                    <li>• These changes are preparing your body for adulthood</li>
                  </ul>
                </div>

                <div className="bg-pink-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Emotional Development</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Developing crushes and romantic feelings</li>
                    <li>• Increased interest in relationships</li>
                    <li>• Confusion about new emotions is normal</li>
                    <li>• Learning about love, attraction, and intimacy</li>
                    <li>• Understanding the difference between love and infatuation</li>
                  </ul>
                </div>
              </div>

              <div className="bg-purple-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">Important Concepts to Understand</h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• <strong>Consent:</strong> Freely given agreement to any physical contact</li>
                    <li>• <strong>Boundaries:</strong> Your right to say no to anything uncomfortable</li>
                    <li>• <strong>Respect:</strong> Treating yourself and others with dignity</li>
                  </ul>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• <strong>Communication:</strong> Talking openly about feelings and boundaries</li>
                    <li>• <strong>Values:</strong> Understanding what's important to you</li>
                    <li>• <strong>Safety:</strong> Protecting your physical and emotional well-being</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Healthy Relationships */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Users className="w-6 h-6 text-green-600" />
              Healthy Romantic Relationships
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                Healthy romantic relationships are built on mutual respect, trust, communication, and shared values. 
                Understanding what makes a relationship healthy helps you make good choices about dating and intimacy.
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-green-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 text-green-700">✅ Healthy Relationship Signs</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Mutual respect for each other's boundaries</li>
                    <li>• Open and honest communication</li>
                    <li>• Support for each other's goals and dreams</li>
                    <li>• Trust and reliability</li>
                    <li>• Equality and shared decision-making</li>
                    <li>• Respect for your right to say "no"</li>
                    <li>• Encouragement to maintain other friendships</li>
                  </ul>
                </div>

                <div className="bg-red-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3 text-red-700">🚩 Warning Signs</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Pressure to do things you're not ready for</li>
                    <li>• Jealousy or possessiveness</li>
                    <li>• Trying to control what you do or who you see</li>
                    <li>• Disrespecting your boundaries</li>
                    <li>• Making you feel guilty for saying "no"</li>
                    <li>• Isolating you from friends and family</li>
                    <li>• Any form of physical, emotional, or sexual abuse</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Consent and Boundaries */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Shield className="w-6 h-6 text-purple-600" />
              Understanding Consent & Personal Boundaries
            </h3>

            <div className="space-y-6">
              <div className="bg-purple-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-800 mb-3">What is Consent?</h4>
                <p className="text-gray-700 text-sm mb-3">
                  Consent is a clear, enthusiastic "yes" to any physical contact or sexual activity. It must be:
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• <strong>Freely given:</strong> No pressure, threats, or manipulation</li>
                    <li>• <strong>Informed:</strong> Understanding what you're agreeing to</li>
                    <li>• <strong>Enthusiastic:</strong> A genuine "yes," not just absence of "no"</li>
                  </ul>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• <strong>Ongoing:</strong> Can be withdrawn at any time</li>
                    <li>• <strong>Specific:</strong> Saying yes to one thing doesn't mean yes to everything</li>
                    <li>• <strong>Sober:</strong> Cannot be given under influence of drugs/alcohol</li>
                  </ul>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-blue-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Your Right to Say No</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• You have the right to say no to any physical contact</li>
                    <li>• You don't need to give reasons for your boundaries</li>
                    <li>• Your comfort level may change, and that's okay</li>
                    <li>• A loving partner will respect your decisions</li>
                    <li>• You can change your mind at any time</li>
                  </ul>
                </div>

                <div className="bg-pink-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Communicating Boundaries</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Be clear and direct about your limits</li>
                    <li>• Use "I" statements to express your feelings</li>
                    <li>• Don't apologize for having boundaries</li>
                    <li>• Trust your instincts if something feels wrong</li>
                    <li>• Seek help if someone doesn't respect your boundaries</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Making Informed Decisions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Lock className="w-6 h-6 text-orange-600" />
              Making Informed Decisions About Intimacy
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                Decisions about physical intimacy are deeply personal and should be made thoughtfully, considering 
                your values, readiness, and the potential consequences. There's no rush - taking time to make 
                informed decisions shows wisdom and self-respect.
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-orange-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Questions to Consider</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Am I ready emotionally and mentally?</li>
                    <li>• Does this align with my values and beliefs?</li>
                    <li>• Am I making this decision freely, without pressure?</li>
                    <li>• Do I trust and respect my partner?</li>
                    <li>• Have we talked openly about boundaries and expectations?</li>
                    <li>• Am I prepared for potential consequences?</li>
                  </ul>
                </div>

                <div className="bg-blue-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Important Considerations</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Physical health and safety</li>
                    <li>• Emotional readiness and maturity</li>
                    <li>• Understanding of potential risks</li>
                    <li>• Access to healthcare and resources</li>
                    <li>• Support system and trusted adults to talk to</li>
                    <li>• Legal considerations and age of consent laws</li>
                  </ul>
                </div>
              </div>

              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                <p className="text-yellow-800 text-sm">
                  <strong>Remember:</strong> Waiting until you're truly ready - emotionally, mentally, and physically - 
                  is a sign of wisdom and self-respect. There's no shame in taking your time.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Safety and Health */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Shield className="w-6 h-6 text-green-600" />
              Sexual Health & Safety
            </h3>

            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-green-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Protecting Your Health</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Regular healthcare checkups are important</li>
                    <li>• Understanding sexually transmitted infections (STIs)</li>
                    <li>• Knowing about pregnancy prevention methods</li>
                    <li>• Importance of honest communication with healthcare providers</li>
                    <li>• Understanding your reproductive health</li>
                  </ul>
                </div>

                <div className="bg-blue-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">Staying Safe</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Trust your instincts about people and situations</li>
                    <li>• Avoid situations where you feel pressured</li>
                    <li>• Have a safety plan when dating</li>
                    <li>• Know how to get help if you need it</li>
                    <li>• Understand the risks of alcohol and drugs</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Getting Support */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-gradient-to-r from-rose-500 to-purple-600 rounded-2xl shadow-lg p-8 text-white"
          >
            <h3 className="text-2xl font-bold mb-6 flex items-center gap-3">
              <AlertCircle className="w-6 h-6" />
              Getting Support & Information
            </h3>

            <div className="space-y-4">
              <p className="leading-relaxed">
                Having questions about sexuality and relationships is completely normal. Don't hesitate to seek 
                accurate information and support from trusted sources.
              </p>

              <div className="bg-white/20 backdrop-blur-sm rounded-xl p-6">
                <h4 className="font-semibold mb-3">Trusted Sources for Information:</h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <ul className="space-y-2 text-sm">
                    <li>• Parents, guardians, or trusted family members</li>
                    <li>• School counselors or health teachers</li>
                    <li>• Healthcare providers (doctors, nurses)</li>
                    <li>• Planned Parenthood or other health clinics</li>
                  </ul>
                  <ul className="space-y-2 text-sm">
                    <li>• Religious or spiritual leaders you trust</li>
                    <li>• Reputable health websites and resources</li>
                    <li>• Teen helplines and support services</li>
                    <li>• Books written by health professionals</li>
                  </ul>
                </div>
              </div>

              <p className="text-rose-100 text-sm">
                Remember: Your sexual health and well-being are important. Seeking information and support shows 
                maturity and self-care!
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default SexEducationPage;
