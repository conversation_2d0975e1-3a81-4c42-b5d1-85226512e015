import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Heart, Users, Lightbulb, BookOpen, Star, CheckCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const UnderstandingYourBodyPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/students-hub');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 pt-16">
      {/* Header */}
      <div className="bg-gradient-to-r from-pink-600 via-purple-600 to-blue-600 py-8 relative">
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        <div className="relative px-6 md:px-12">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20"
            >
              <ArrowLeft size={20} />
              <span>Back to Hub</span>
            </button>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">
                👧 Understanding Your Body
              </h1>
              <p className="text-pink-100 mt-2 text-lg drop-shadow-md">
                A comprehensive guide to female anatomy, puberty changes, and body positivity
              </p>
            </div>
          </div>
        </div>
        {/* Sharp Silver Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="px-6 md:px-12 py-12">
          {/* Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-pink-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-pink-400/30">
                <Heart className="w-6 h-6 text-pink-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white drop-shadow-lg">Welcome to Your Journey</h2>
                <p className="text-gray-300">Understanding your body is the first step to loving yourself</p>
              </div>
            </div>

            <p className="text-gray-200 leading-relaxed mb-6 drop-shadow-sm">
              Growing up as a young woman is an incredible journey filled with changes, discoveries, and new experiences.
              Your body is amazing, and understanding how it works will help you feel confident, healthy, and empowered
              throughout your teenage years and beyond.
            </p>

            <div className="bg-pink-500/10 backdrop-blur-sm border-l-4 border-pink-400 p-4 rounded-r-lg border border-pink-400/20">
              <p className="text-pink-200 font-medium drop-shadow-sm">
                💡 Remember: Every girl's body is different, and that's perfectly normal! This guide will help you
                understand the common changes that happen during puberty and how to take care of yourself.
              </p>
            </div>
            {/* Sharp Silver Divider */}
            <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
          </motion.div>

          {/* Puberty Changes Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 mb-8 border border-gray-700/50"
          >
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3 drop-shadow-lg">
              <Star className="w-6 h-6 text-purple-400" />
              Understanding Puberty Changes
            </h3>

            <div className="space-y-6">
              <div className="border-l-4 border-purple-400 pl-6 bg-purple-500/10 backdrop-blur-sm rounded-r-xl p-4">
                <h4 className="text-lg font-semibold text-white mb-3 drop-shadow-md">Physical Changes You Might Notice</h4>
                <ul className="space-y-2 text-gray-200">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Growth spurts:</strong> You might grow taller quickly and notice your body shape changing</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Breast development:</strong> This usually starts between ages 8-13 and can take several years</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Body hair growth:</strong> Hair will start growing in new places like underarms and legs</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Skin changes:</strong> You might notice oily skin or acne as your hormones change</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Voice changes:</strong> Your voice might crack or change pitch occasionally</span>
                  </li>
                </ul>
              </div>

              <div className="border-l-4 border-blue-400 pl-6 bg-blue-500/10 backdrop-blur-sm rounded-r-xl p-4">
                <h4 className="text-lg font-semibold text-white mb-3 drop-shadow-md">Emotional Changes</h4>
                <ul className="space-y-2 text-gray-200">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Mood swings:</strong> Feeling happy one moment and sad the next is completely normal</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">New feelings:</strong> You might start having crushes or feeling differently about relationships</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Independence:</strong> Wanting more privacy and independence from your family</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong className="text-white">Self-consciousness:</strong> Feeling more aware of how you look and what others think</span>
                  </li>
                </ul>
              </div>
            </div>
            {/* Sharp Silver Divider */}
            <div className="mt-8 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
          </motion.div>

          {/* Body Positivity Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Heart className="w-6 h-6 text-pink-600" />
              Loving Your Body
            </h3>

            <div className="space-y-6">
              <p className="text-gray-700 leading-relaxed">
                Your body is unique and beautiful just the way it is! During puberty, it's normal to feel uncertain 
                about the changes happening to you. Remember that everyone develops at their own pace, and there's 
                no "right" way for your body to look.
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-pink-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">✨ Body Positivity Tips</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Focus on what your body can do, not just how it looks</li>
                    <li>• Avoid comparing yourself to others or images in media</li>
                    <li>• Practice positive self-talk and affirmations</li>
                    <li>• Surround yourself with supportive friends and family</li>
                    <li>• Remember that beauty comes in all shapes and sizes</li>
                  </ul>
                </div>

                <div className="bg-purple-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-800 mb-3">🌟 Daily Affirmations</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• "My body is strong and capable"</li>
                    <li>• "I am beautiful inside and out"</li>
                    <li>• "I treat my body with kindness and respect"</li>
                    <li>• "I am growing into the person I'm meant to be"</li>
                    <li>• "I celebrate what makes me unique"</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Taking Care of Your Body */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <Lightbulb className="w-6 h-6 text-yellow-600" />
              Taking Care of Yourself
            </h3>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-8 h-8 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">Healthy Eating</h4>
                <p className="text-gray-600 text-sm">
                  Eat a balanced diet with fruits, vegetables, whole grains, and protein to fuel your growing body.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">Stay Active</h4>
                <p className="text-gray-600 text-sm">
                  Regular exercise helps you feel strong, confident, and can improve your mood and energy levels.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="w-8 h-8 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">Get Enough Sleep</h4>
                <p className="text-gray-600 text-sm">
                  Aim for 8-10 hours of sleep each night to help your body and mind develop properly.
                </p>
              </div>
            </div>
          </motion.div>

          {/* When to Talk to Someone */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gradient-to-r from-pink-500 to-purple-600 rounded-2xl shadow-lg p-8 text-white"
          >
            <h3 className="text-2xl font-bold mb-6 flex items-center gap-3">
              <Users className="w-6 h-6" />
              When to Seek Support
            </h3>

            <div className="space-y-4">
              <p className="leading-relaxed">
                It's important to have trusted adults you can talk to about the changes in your body. Don't hesitate 
                to reach out if you have questions or concerns.
              </p>

              <div className="bg-white/20 backdrop-blur-sm rounded-xl p-6">
                <h4 className="font-semibold mb-3">Talk to a trusted adult if:</h4>
                <ul className="space-y-2 text-sm">
                  <li>• You have questions about the changes in your body</li>
                  <li>• You're experiencing severe pain or discomfort</li>
                  <li>• You're feeling very worried or anxious about your body</li>
                  <li>• You need help with hygiene or health products</li>
                  <li>• You want to learn more about staying healthy</li>
                </ul>
              </div>

              <p className="text-pink-100 text-sm">
                Remember: There's no such thing as a silly question when it comes to your health and well-being!
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default UnderstandingYourBodyPage;
